server:
  port: 20021

--- #################### 数据库相关配置 ####################

spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - de.codecentric.boot.admin.server.config.AdminServerAutoConfiguration # 禁用 Spring Boot Admin 的 Server 的自动配置
      - de.codecentric.boot.admin.server.ui.config.AdminServerUiAutoConfiguration # 禁用 Spring Boot Admin 的 Server UI 的自动配置
      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration # 禁用 Spring Boot Admin 的 Client 的自动配置
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: **********************************************************************************************************************************************************************************************
          username: irrigation
          password: MatrZVk@8h^U

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: ************** # 地址
      port: 8210 # 端口
      database: 8 # 数据库索引
      password: 7194898 # 密码，建议生产环境开启
      redisson:
        # 单节点配置
        singleServerConfig:
          # 连接空闲超时，单位：毫秒
          idleConnectionTimeout: 10000
          # 连接超时，单位：毫秒
          connectTimeout: 10000
          # 命令等待超时，单位：毫秒
          timeout: 10000
          # 命令失败重试次数,如果尝试达到 retryAttempts（命令失败重试次数） 仍然不能将命令发送至某个指定的节点时，将抛出错误。
          # 如果尝试在此限制之内发送成功，则开始启用 timeout（命令等待超时） 计时。
          retryAttempts: 3
          # 命令重试发送时间间隔，单位：毫秒
          retryInterval: 1500
          # 密码 如果为空，必须将该行注释掉
          password: ${spring.redis.password}
          # 客户端名称
          clientName: null
          # 单个连接最大订阅数量
          subscriptionsPerConnection: 5
          # 节点地址
          address: redis://${spring.redis.host}:${spring.redis.port}
          # 发布和订阅连接的最小空闲连接数
          subscriptionConnectionMinimumIdleSize: 1
          # 发布和订阅连接池大小
          subscriptionConnectionPoolSize: 50
          # 最小空闲连接数
          connectionMinimumIdleSize: 32
          # 连接池大小
          connectionPoolSize: 64
          # 数据库编号
          database: 8
          # DNS监测时间间隔，单位：毫秒
          dnsMonitoringInterval: 5000
          # 线程池数量,默认值: 当前处理核数量 * 2
          #threads: 0
          # Netty线程池数量,默认值: 当前处理核数量 * 2
          #nettyThreads: 0
          # 编码
        #codec: !<org.redisson.codec.JsonJacksonCodec> { }
        # 传输模式
        transportMode: "NIO"
--- #################### 消息队列相关配置 ####################

spring:
  rabbitmq:
    enable: true
    host: **************
    port: 8272
    username: irrigation
    password: huangshi
    virtual-host: /
    listener:
      simple:
        default-requeue-rejected: false

--- #################### 定时任务相关配置 ####################

# Quartz 配置项，对应 QuartzProperties 配置类
spring:
  quartz:
    auto-startup: true # 测试环境，需要开启 Job
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: true # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

# Resilience4j 配置项
resilience4j:
  ratelimiter:
    instances:
      backendA:
        limit-for-period: 1 # 每个周期内，允许的请求数。默认为 50
        limit-refresh-period: 60s # 每个周期的时长，单位：微秒。默认为 500
        timeout-duration: 1s # 被限流时，阻塞等待的时长，单位：微秒。默认为 5s
        register-health-indicator: true # 是否注册到健康监测

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  file:
    name: ${user.home}/logs/${spring.application.name} # 日志文件名，全路径
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    com.yutoudev.irrigation.module.infra.dal.mysql: error
    com.yutoudev.irrigation.module.infra.dal.mysql.job.JobLogMapper: error # 配置 JobLogMapper 的日志级别为 info
    com.yutoudev.irrigation.module.system.dal.mysql: error
    com.yutoudev.irrigation.module.tool.dal.mysql: error
    com.yutoudev.irrigation.cms.dal.mysql: error
    com.yutoudev.irrigation.framework.common: error
    com.yutoudev.irrigation.irr.dal.mysql: debug

--- #################### 微信公众号相关配置 ####################
wx: # 参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-mp-spring-boot-starter/README.md 文档
  mp:
    # 公众号配置(必填)
    app-id: wx041349c6f39b268b
    secret: 5abee519483bc9f8cb37ce280e814bd0
    # 存储配置，解决 AccessToken 的跨节点的共享
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wx # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
  miniapp: # 小程序配置（必填），参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-miniapp-spring-boot-starter/README.md 文档
    appid: wx63c280fe3248a3e7
    secret: 6f270509224a7ae1296bbf1c8cb97aed
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wa # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台

--- #################### 晓创相关配置 ####################

# 晓创配置项，设置当前项目所有自定义的配置
irrigation:
  captcha:
    enable: false # 本地环境，暂时关闭图片验证码，方便登录等接口的测试；
    admin-enable: false
    app-enable: false
    pad-enable: false
  security:
    mock-enable: true
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  access-log: # 访问日志的配置项
    enable: false
  error-code: # 错误码相关配置项
    enable: false
  word:
    exportPath: /home/<USER>/export
    templatePath: /template/
    wordPath: /word/
    zipPath: /zip/
  cmcc:
    iot-card:
      appid: 731120327731731000
      password: I86zjksZu
  artemis:
    host: v.hngqyun.cn:1443
    appKey: 23395495
    appSecret: 2HWL6wrunLzJlWeEOl2D
    playUrl: /api/video/v2/cameras/previewURLs
    operatorCameraUrl: /api/video/v1/ptzs/controlling
    operatorBlowUpUrl: /api/video/v1/ptzs/selZoom
  iot:
    encrypt:
      key: 0123456789ABCDEF0123456789ABCDEF
      vector: A23DFHz#353YwIO=
  hnwrd:
    url: http://************:8090/sqetl-receive/message/send
    appId: hsskdb
    appSecret: 5beb4e9f6c53333c468b6d8188f042c4
  province-platform:
    host: http://*************:8089
    client-id: 10000
    client-secret: 10001
    redirect-uri: https://hssk.hngqyun.cn:8200/pp-sso-login
  demo: false # 关闭演示模式

wx:
  mp:
    useRedis: false
    defaultContent: \u60A8\u597D\uFF0C\u6709\u4EC0\u4E48\u95EE\u9898\uFF1F
    redisConfig:
      host: 127.0.0.1
      port: 6379
      password:


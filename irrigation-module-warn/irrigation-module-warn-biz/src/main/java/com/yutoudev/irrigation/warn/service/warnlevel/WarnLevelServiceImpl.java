package com.yutoudev.irrigation.warn.service.warnlevel;

import cn.hutool.core.util.ObjectUtil;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.warn.controller.admin.warnlevel.vo.*;
import com.yutoudev.irrigation.warn.controller.admin.warntype.vo.typeequip.WarnTypeEquipUpdateReqVO;
import com.yutoudev.irrigation.warn.convert.warnlevel.WarnLevelConvert;
import com.yutoudev.irrigation.warn.convert.warntype.WarnTypeEquipConvert;
import com.yutoudev.irrigation.warn.dal.dataobject.warnlevel.WarnLevelDO;
import com.yutoudev.irrigation.warn.dal.dataobject.warntype.WarnTypeEquipDO;
import com.yutoudev.irrigation.warn.dal.mysql.warnlevel.WarnLevelMapper;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.warn.enums.ErrorCodeConstants.*;

/**
 * 预警等级Service实现类
 *
 * <AUTHOR>
 * @description 管理后台-预警等级Service实现类
 * @time 2024-06-26 13:53:51
 */
@Service
@Validated
public class WarnLevelServiceImpl extends TalonsServiceImpl<WarnLevelMapper, WarnLevelDO> implements WarnLevelService<WarnLevelDO> {

    @Resource
    private WarnLevelMapper warnLevelMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Override
    public void checkField(WarnLevelDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(WarnLevelCreateReqVO createReqVO) {
        // 插入
        WarnLevelDO warnLevel = WarnLevelConvert.INSTANCE.convert(createReqVO);
        this.save(warnLevel, true);
        // 返回
        return warnLevel.getId();
    }

    @Override
    public boolean createBatch(List<WarnLevelCreateReqVO> list) {
        if (list != null && !list.isEmpty()) {
            baseMapper.delete(new LambdaQueryWrapperX<WarnLevelDO>().eq(WarnLevelDO::getWarnTypeId, list.get(0).getWarnTypeId()));
        }
        List<WarnLevelDO> saveList = WarnLevelConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        } else {
            throw exception(WARN_LEVEL_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public void update(WarnLevelUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateWarnLevelExists(updateReqVO.getId());
        // 更新
        WarnLevelDO updateObj = WarnLevelConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    @Transactional
    public boolean updateBatch(List<WarnLevelUpdateReqVO> list) {
        if (list == null || list.isEmpty()){
            throw exception(WARN_LEVEL_UPDATE_BATCH_ERROR);
        }
        List<WarnLevelUpdateReqVO> uList = list.stream().filter(item -> ObjectUtil.isNotEmpty(item.getId())).collect(Collectors.toList());
        List<WarnLevelUpdateReqVO> iList = list.stream().filter(item -> ObjectUtil.isEmpty(item.getId())).collect(Collectors.toList());

        List<Long> modIds = uList.stream().map(WarnLevelUpdateReqVO::getId).collect(Collectors.toList());
        List<WarnLevelDO> all = list(new LambdaQueryWrapperX<WarnLevelDO>().eq(WarnLevelDO::getWarnTypeId, list.get(0).getWarnTypeId()));
        List<Long> delIds = all.stream().map(WarnLevelDO::getId).collect(Collectors.toList());
        delIds.removeIf(id -> modIds.contains(id));

        List<WarnLevelDO> saveList = WarnLevelConvert.INSTANCE.convertUpdateBatch(iList);
        List<WarnLevelDO> updateList = WarnLevelConvert.INSTANCE.convertUpdateBatch(uList);


        boolean del = false;
        boolean mod = false;
        boolean add = false;
        if (!delIds.isEmpty()) {
            del = this.removeByIds(delIds, true);
        } else {
            del = true;
        }

        if (!uList.isEmpty()) {
            for (WarnLevelDO tempDO : updateList) {
                // 校验存在,因为存进来转化就是UpdateReqVO
                this.validateWarnLevelExists(tempDO.getId());
            }
            mod = this.updateBatchById(updateList, true);
        } else {
            mod = true;
        }

        if (!saveList.isEmpty()) {
            add = this.saveBatch(saveList, true);
        } else {
            add = true;
        }

        if (del && mod && add) {
            return true;
        } else {
            throw exception(WARN_LEVEL_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateWarnLevelExists(id);
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(WARN_LEVEL_DELETE_BATCH_ERROR);
        }
    }

    private void validateWarnLevelExists(Long id) {
        if (warnLevelMapper.selectById(id) == null) {
            throw exception(WARN_LEVEL_NOT_EXISTS);
        }
    }

    @Override
    public WarnLevelDO get(Long id) {
        return this.getById(id, true);
    }

    @Override
    public List<WarnLevelDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public PageResult<WarnLevelDO> page(WarnLevelPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<WarnLevelDO> queryBuilder = new QueryCriteriaWrapperBuilder<WarnLevelDO>() {
        };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<WarnLevelDO> pageResult = warnLevelMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
        talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<WarnLevelDO> getList(WarnLevelQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<WarnLevelDO> queryBuilder = new QueryCriteriaWrapperBuilder<WarnLevelDO>() {
        };
        queryBuilder.build(queryReqVO);

        List<WarnLevelDO> result = warnLevelMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<WarnLevelExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(WARN_LEVEL_IMPORT_LIST_IS_EMPTY);
        }

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<WarnLevelDO> saveList = WarnLevelConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            WarnLevelDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, WarnLevelExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}

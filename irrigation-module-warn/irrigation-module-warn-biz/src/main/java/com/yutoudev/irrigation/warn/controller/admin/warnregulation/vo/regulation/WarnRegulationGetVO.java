package com.yutoudev.irrigation.warn.controller.admin.warnregulation.vo.regulation;

import com.yutoudev.irrigation.warn.dal.dataobject.warnregulation.WarnRegulationUserDO;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 预警规则 Base VO
 *
 * <AUTHOR>
 * @description 预警规则 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-06-26 14:24:28
 */
@Data
public class WarnRegulationGetVO extends WarnRegulationBaseVO {

    /**
     * 预警数据来源id
     */
    @NotNull(message = "预警分组id不能为空")
    private Long id;

    private List<WarnRegulationUserDO> users;

}

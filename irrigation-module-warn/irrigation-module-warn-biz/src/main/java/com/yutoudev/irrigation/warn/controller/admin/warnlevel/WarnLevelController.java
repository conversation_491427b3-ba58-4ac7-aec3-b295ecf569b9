package com.yutoudev.irrigation.warn.controller.admin.warnlevel;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.warn.controller.admin.warnlevel.vo.*;
import com.yutoudev.irrigation.warn.convert.warnlevel.WarnLevelConvert;
import com.yutoudev.irrigation.warn.dal.dataobject.warnlevel.WarnLevelDO;
import com.yutoudev.irrigation.warn.service.warnlevel.WarnLevelService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 预警等级
 *
 * <AUTHOR>
 * @description 管理后台-预警等级controller
 * @time 2024-06-26 13:53:51
 */
@RestController
@RequestMapping("/ew/warn-level")
@Validated
public class WarnLevelController {

    private static final String MODULE_NAME = "预警等级";

    @Resource
    private WarnLevelService<WarnLevelDO> warnLevelService;

    /**
     * 创建预警等级
     *
     * @param createReqVO WarnLevelCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody WarnLevelCreateReqVO createReqVO) {
        return success(warnLevelService.create(createReqVO));
    }

    /**
     * 批量创建预警等级
     *
     * @param lists WarnLevelCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<WarnLevelCreateReqVO> lists) {
        return success(warnLevelService.createBatch(lists));
    }

    /**
     * 更新预警等级
     *
     * @param updateReqVO WarnLevelUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody WarnLevelUpdateReqVO updateReqVO) {
        warnLevelService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新预警等级
     *
     * @param lists 批量更新列表 WarnLevelUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<WarnLevelUpdateReqVO> lists) {
        return success(warnLevelService.updateBatch(lists));
    }

    /**
     * 删除预警等级
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        warnLevelService.delete(id);
        return success(true);
    }

    /**
     * 批量删除预警等级
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(warnLevelService.deleteBatch(ids));
    }

    /**
     * 获得预警等级详情
     *
     * @param id 编号 Long
     * @return CommonResult<WarnLevelDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<WarnLevelDetailRespVO> get(@RequestParam("id") Long id) {
        WarnLevelDO warnLevel = warnLevelService.get(id);
        return success(WarnLevelConvert.INSTANCE.convertDetail(warnLevel));
    }

    /**
     * 预警等级列表
     *
     * @param queryReqVO 查询条件 WarnLevelQueryReqVO
     * @return CommonResult<List < WarnLevelRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<WarnLevelRespVO>> getList(@RequestQueryParam WarnLevelQueryReqVO queryReqVO) {
        List<WarnLevelDO> list = warnLevelService.getList(queryReqVO);
        return success(WarnLevelConvert.INSTANCE.convertList(list));
    }

    /**
     * 预警等级分页
     *
     * @param pageVO 查询条件 WarnLevelPageReqVO
     * @return CommonResult<PageResult < WarnLevelRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<WarnLevelRespVO>> page(@RequestQueryParam WarnLevelPageReqVO pageVO) {
        PageResult<WarnLevelDO> pageResult = warnLevelService.page(pageVO);
        return success(WarnLevelConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出预警等级Excel
     *
     * @param queryReqVO 查询条件 WarnLevelExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam WarnLevelExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<WarnLevelDO> list = warnLevelService.getList(queryReqVO);
        // 导出 Excel
        List<WarnLevelExcelVO> datas = WarnLevelConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "预警等级", "xlsx"), queryReqVO.getExportSheetName(),
                WarnLevelExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入预警等级模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "预警等级-导入模版.xls", "sheet1", WarnLevelExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入预警等级Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('ew:warn-level:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<WarnLevelExcelVO> list = ExcelUtils.read(file, WarnLevelExcelVO.class);
        return success(warnLevelService.importExcel(list, isUpdate));
    }
}
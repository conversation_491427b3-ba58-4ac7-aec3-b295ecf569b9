package com.yutoudev.irrigation.warn.dal.dataobject.warninfo;


import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value = "ew_warn_info_flicker", autoResultMap = true)
@KeySequence("ew_warn_info_flicker_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarnInfoFlickerDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    private Integer warnType;

    /**
     * 设备id
     */
    private Long equipId;

    /**
     * 预警组id
     */
    private Long warnGroupId;

    /**
     * 预警类型id
     */
    private Long warnTypeId;

    /**
     * 预警等级id
     */
    private Long warnLevelId;

    private String warnContent;

    /**
     * 颜色显示
     */
    private String warnColor;

    /**
     * 预警种类 1：超警戒水位 2：枯水水位
     */
    private Integer regulationType;

////    @TableField(exist = false)
//    private LocalDateTime createTime;

    @TableField(exist = false)
    private String creator;

    @TableField(exist = false)
    private String updater;

    @TableField(exist = false)
    private LocalDateTime updateTime;

}

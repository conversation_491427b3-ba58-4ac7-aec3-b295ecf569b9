package com.yutoudev.irrigation.warn.service.warnrecord;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.warn.controller.admin.warnrecord.vo.*;
import com.yutoudev.irrigation.warn.dal.dataobject.warnrecord.WarnRecordDO;
import io.github.portaldalaran.talons.core.ITalonsService;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 预警记录Service接口
 *
 * <AUTHOR>
 * @description 管理后台-预警记录Service接口，对ITalonsService的扩展
 * @time 2024-06-26 14:24:26
 */
public interface WarnRecordService<T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建预警记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid WarnRecordCreateReqVO createReqVO);

    /**
     * 批量创建预警记录
     *
     * @param list 创建信息
     * @return 编号
     */
    boolean createBatch(@Valid List<WarnRecordCreateReqVO> list);

    /**
     * 更新预警记录
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid WarnRecordUpdateReqVO updateReqVO);

    /**
     * 批量更新预警记录
     *
     * @param list 更新信息
     */
    boolean updateBatch(@Valid List<WarnRecordUpdateReqVO> list);

    /**
     * 删除预警记录
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
     * 批量删除预警记录
     *
     * @param ids 编号
     */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得预警记录
     *
     * @param id 编号
     * @return 预警记录
     */
    WarnRecordDO get(Long id);

    /**
     * 预警记录列表
     *
     * @param ids 编号
     * @return 预警记录列表
     */
    List<WarnRecordDO> getList(List<Long> ids);

    /**
     * 预警记录分页
     *
     * @param pageReqVO 分页查询
     * @return 预警记录分页
     */
    PageResult<WarnRecordDO> page(WarnRecordPageReqVO pageReqVO);

    /**
     * 获得预警记录列表,
     *
     * @param queryReqVO 查询条件键值对
     * @return 预警记录列表
     */
    List<WarnRecordDO> getList(WarnRecordQueryReqVO queryReqVO);

    /**
     * 批量导入预警记录 excel
     *
     * @param importList      导入预警记录列表
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    ImportExcelRespVO importExcel(List<WarnRecordExcelVO> importList, boolean isUpdateSupport);
}
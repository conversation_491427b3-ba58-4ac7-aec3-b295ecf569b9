package com.yutoudev.irrigation.regulation.expression;

import cn.hutool.extra.spring.SpringUtil;
import com.yutoudev.irrigation.irr.api.chanbase.ChanBaseApi;
import com.yutoudev.irrigation.irr.api.chanbase.dto.ChanBaseDTO;
import com.yutoudev.irrigation.irr.api.equipbase.EquipBaseApi;
import com.yutoudev.irrigation.irr.api.equipbase.dto.ChanEquipDetailDTO;
import com.yutoudev.irrigation.irr.api.equipbase.dto.EquipBaseDTO;
import com.yutoudev.irrigation.irr.api.swhs.SwhsBaseApi;
import com.yutoudev.irrigation.irr.api.swhs.dto.SwhsBaseDTO;
import com.yutoudev.irrigation.regulation.analyze.Condition;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.warn.enums.ErrorCodeConstants.CONDITION_ERROR;

public abstract class BaseExpression {

    protected Condition condition;

    public BaseExpression(Condition condition) {
        this.condition = condition;
    }

    public abstract boolean test(Map<String, Object> variable);

    protected String getValue(Object value1, String value, Map<String, Object> variable) {
        if (value == null) {
            return value;
        }
        if (("__chanWarnWaterLevel__").equals(value.trim())) {//渠道告警水位 desDepth
            EquipBaseApi equipBaseApi = SpringUtil.getBean(EquipBaseApi.class);
            ChanBaseApi chanBaseApi = SpringUtil.getBean(ChanBaseApi.class);
            EquipBaseDTO equip = (EquipBaseDTO) variable.get("__equip__");
            List<ChanEquipDetailDTO> chanEquipDetailDTOS = equipBaseApi.listChan(equip.getId());
            if (chanEquipDetailDTOS == null || chanEquipDetailDTOS.size() == 0) {
                throw exception(CONDITION_ERROR);
            }
            ChanBaseDTO chanBase = chanBaseApi.getChanBaseById(chanEquipDetailDTOS.get(0).getChanId());
//            if (chanBase == null || chanBase.getDesDepth() == null) {
//                throw exception(CONDITION_ERROR);
//            }
//            value = String.valueOf(chanBase.getDesDepth());
            if (chanBase == null || chanBase.getMaxWarningValue() == null) {
                throw exception(CONDITION_ERROR);
            }
            value = String.valueOf(chanBase.getMaxWarningValue());
            variable.put("____chanWarnWaterLevel____", sub(value1, value));
            variable.put("name", chanBase.getChanName());
            variable.put("regulationType", "1");//超警戒水位
            return value;
        } else if (("__swhsFloodFlow__").equals(value.trim())) {//水库防洪流量
            Long id = (Long) variable.get("__id__");
            return value;
        } else if (("__swhsFloodWaterLevel__").equals(value.trim())) {//水库防洪水位
            SwhsBaseApi swhsBaseApi = SpringUtil.getBean(SwhsBaseApi.class);
            EquipBaseDTO equip = (EquipBaseDTO) variable.get("__equip__");
            SwhsBaseDTO swhsBase = swhsBaseApi.getSwhsBaseById(equip.getSwhsId());
            if (swhsBase == null || swhsBase.getFloodUpperLevel() == null) {
                throw exception(CONDITION_ERROR);
            }
            value = String.valueOf(swhsBase.getFloodUpperLevel());
            variable.put("____swhsFloodWaterLevel____", sub(value1, value));
            variable.put("name", swhsBase.getSwhsName());
            variable.put("regulationType", "1");//超警戒水位
            return value;
        } else if (("__swhsLowFlow__").equals(value.trim())) {//水库枯水流量
            SwhsBaseApi swhsBaseApi = SpringUtil.getBean(SwhsBaseApi.class);
            EquipBaseDTO equip = (EquipBaseDTO) variable.get("__equip__");
            SwhsBaseDTO swhsBase = swhsBaseApi.getSwhsBaseById(equip.getSwhsId());
            if (swhsBase == null || swhsBase.getLowestTraffic() == null) {
                throw exception(CONDITION_ERROR);
            }
            value = String.valueOf(swhsBase.getLowestTraffic());
            variable.put("____swhsLowFlow____", sub(value1, value));
            variable.put("name", swhsBase.getSwhsName());
            variable.put("regulationType", "2");//枯水水位
            return value;
        } else if ( ("__swhsLowWaterLevel__").equals(value.trim())) {//水库枯水水位
            SwhsBaseApi swhsBaseApi = SpringUtil.getBean(SwhsBaseApi.class);
            EquipBaseDTO equip = (EquipBaseDTO) variable.get("__equip__");
            SwhsBaseDTO swhsBase = swhsBaseApi.getSwhsBaseById(equip.getSwhsId());
            if (swhsBase == null || swhsBase.getLowestWaterLevel() == null) {
                throw exception(CONDITION_ERROR);
            }
            value = String.valueOf(swhsBase.getLowestWaterLevel());
            variable.put("____swhsLowWaterLevel____", sub(value1, value));
            variable.put("name", swhsBase.getSwhsName());
            variable.put("regulationType", "2");//枯水水位
            return value;
        } else {
            return value;
        }
    }

    private String sub(Object value1, String value2) {
        double difference = Double.parseDouble(value1.toString()) - Double.parseDouble(value2);

        // 计算绝对值
        double absoluteDifference = Math.abs(difference);

        // 使用BigDecimal保留一位小数
        BigDecimal bigDecimal = new BigDecimal(absoluteDifference);
        bigDecimal = bigDecimal.setScale(1, RoundingMode.HALF_UP); // 保留一位小数，四舍五入
        return bigDecimal.toString();
    }


    public static BaseExpression getInstance(Condition condition) {
        try {
            switch (condition.getValueType()) {
                case STRING:
                    return new StrExpression(condition);
                case NUMBER:
                    return new NumExpression(condition);
                case BOOLEAN:
                    return new BooleanExpression(condition);
                case DATE:
                    return new DateExpression(condition);
                case TIME:
                    return new TimeExpression(condition);
                case DATETIME:
                    return new DateTimeExpression(condition);
                case ARRAY:
                    return new ArrayExpression(condition);
                default:
                    //不在计划内的表达式 按默认不成立方式返回
                    return new NullExperssion(condition);
            }
        } catch (Exception e) {
            throw exception(CONDITION_ERROR);
        }

    }

}

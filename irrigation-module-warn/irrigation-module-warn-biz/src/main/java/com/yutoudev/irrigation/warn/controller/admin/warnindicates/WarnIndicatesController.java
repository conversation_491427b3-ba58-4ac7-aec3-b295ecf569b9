package com.yutoudev.irrigation.warn.controller.admin.warnindicates;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.warn.controller.admin.warnindicates.vo.WarnIndicatesRespVO;
import com.yutoudev.irrigation.warn.controller.admin.warnindicates.vo.WarnIndicatesQueryReqVO;
import com.yutoudev.irrigation.warn.convert.warnIndicates.WarnIndicatesConvert;
import com.yutoudev.irrigation.warn.dal.dataobject.equipindicates.WarnIndicatesDO;
import com.yutoudev.irrigation.warn.service.warnindicates.WarnIndicatesService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 *
 * 预警设备上报指标说明
 * @description 管理后台-设备上报指标说明controller
 * <AUTHOR>
 * @time 2024-05-12 21:02:04
 *
 */
@RestController
@RequestMapping("/ew/warn-indicates")
@Validated
public class WarnIndicatesController {

    private static final String MODULE_NAME = "预警设备上报指标说明";

    @Resource
    private WarnIndicatesService<WarnIndicatesDO> warnIndicatesService;



    /**
     * 预警设备上报指标说明列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 EquipIndicatesQueryReqVO
     * @return CommonResult<List<EquipIndicatesRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<WarnIndicatesRespVO>> getList(@RequestQueryParam WarnIndicatesQueryReqVO queryReqVO) {
        List<WarnIndicatesDO> list = warnIndicatesService.getList(queryReqVO);
        return success(WarnIndicatesConvert.INSTANCE.convertList(list));
    }

}
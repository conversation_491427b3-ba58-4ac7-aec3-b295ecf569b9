package com.yutoudev.irrigation.warn.controller.admin.warninfo.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 预警信息 Base VO
 *
 * <AUTHOR>
 * @description 预警信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-06-26 14:24:22
 */
@Data
public class WarnInfoBaseVO {

    /**
     * 设备id
     */
    private Long equipId;

    /**
     * 设备上报指标代码
     */
    @NotNull(message = "设备上报指标代码不能为空")
    private String typeCode;

    /**
     * 预警规则id
     */
    @NotNull(message = "预警规则id不能为空")
    private Long warnRegulationId;

    /**
     * 预警类型id
     */
    @NotNull(message = "预警类型id不能为空")
    private Long warnTypeId;

    /**
     * 预警等级id
     */
    @NotNull(message = "预警等级id不能为空")
    private Long warnLevelId;

    /**
     * 颜色显示
     */
    private String warnColor;

    /**
     * 预警内容
     */
    @NotNull(message = "预警内容不能为空")
    private String warnContent;

    /**
     * 预警状态
     *
     * @mock 1：开启 2：关闭
     */
    @NotNull(message = "预警状态不能为空")
    private Integer status;

    /**
     * 关闭时间
     */
    private LocalDateTime closeDate;
}

package com.yutoudev.irrigation.module.system.service.dept;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.annotations.VisibleForTesting;
import com.yutoudev.irrigation.framework.common.enums.CommonStatusEnum;
import com.yutoudev.irrigation.framework.datapermission.core.annotation.DataPermission;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.framework.security.core.util.SecurityFrameworkUtils;
import com.yutoudev.irrigation.framework.tenant.core.aop.TenantIgnore;
import com.yutoudev.irrigation.framework.tenant.core.aop.TenantIgnoreAdmin;
import com.yutoudev.irrigation.framework.tenant.core.util.TenantUtils;
import com.yutoudev.irrigation.module.system.controller.admin.dept.vo.dept.DeptCreateReqVO;
import com.yutoudev.irrigation.module.system.controller.admin.dept.vo.dept.DeptListReqVO;
import com.yutoudev.irrigation.module.system.controller.admin.dept.vo.dept.DeptUpdateReqVO;
import com.yutoudev.irrigation.module.system.controller.admin.dept.vo.dept.DeptsAndUsersRespVO;
import com.yutoudev.irrigation.module.system.convert.dept.DeptConvert;
import com.yutoudev.irrigation.module.system.convert.user.UserConvert;
import com.yutoudev.irrigation.module.system.dal.dataobject.dept.DeptDO;
import com.yutoudev.irrigation.module.system.dal.dataobject.user.AdminUserDO;
import com.yutoudev.irrigation.module.system.dal.mysql.dept.DeptMapper;
import com.yutoudev.irrigation.module.system.dal.mysql.user.AdminUserMapper;
import com.yutoudev.irrigation.module.system.dal.redis.RedisKeyConstants;
import com.yutoudev.irrigation.module.system.enums.dept.DeptIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.framework.common.util.collection.CollectionUtils.convertSet;
import static com.yutoudev.irrigation.module.system.enums.ErrorCodeConstants.*;

/**
 * 部门 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeptServiceImpl implements DeptService {

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST,
            allEntries = true) // allEntries 清空所有缓存，因为操作一个部门，涉及到多个缓存
    public Long createDept(DeptCreateReqVO reqVO) {

        // 校验正确性
        if (reqVO.getParentId() == null) {
            reqVO.setParentId(DeptIdEnum.ROOT.getId());
        }

        Long tenantId = null;
        if (SecurityFrameworkUtils.isAdmin()) {
            DeptDO deptDO = TenantUtils.executeIgnore(() -> {
                return deptMapper.selectById(reqVO.getParentId());
            });
            tenantId = deptDO.getTenantId();
        }

        validateForCreateOrUpdate(null, reqVO.getParentId(), reqVO.getName());
        // 插入部门
        DeptDO dept = DeptConvert.INSTANCE.convert(reqVO);
        TenantUtils.execute(tenantId, () -> {
            deptMapper.insert(dept);
        });

        return dept.getId();
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST,
            allEntries = true) // allEntries 清空所有缓存，因为操作一个部门，涉及到多个缓存
    public void updateDept(DeptUpdateReqVO reqVO) {

        // 校验正确性
        if (reqVO.getParentId() == null) {
            reqVO.setParentId(DeptIdEnum.ROOT.getId());
        }

        Long tenantId = null;
        if (SecurityFrameworkUtils.isAdmin()) {
            DeptDO deptDO = TenantUtils.executeIgnore(() -> {
                return deptMapper.selectById(reqVO.getId());
            });
            tenantId = deptDO.getTenantId();
        }

        validateForCreateOrUpdate(reqVO.getId(), reqVO.getParentId(), reqVO.getName());
        // 更新部门
        DeptDO updateObj = DeptConvert.INSTANCE.convert(reqVO);
        if (tenantId != null) {
            updateObj.setTenantId(tenantId);
        }
        TenantUtils.executeIgnore(() -> {
            deptMapper.updateById(updateObj);
        });

    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST,
            allEntries = true) // allEntries 清空所有缓存，因为操作一个部门，涉及到多个缓存
    @TenantIgnoreAdmin
    public void deleteDept(Long id) {
        // 校验是否存在
        validateDeptExists(id);
        // 校验是否有子部门
        if (deptMapper.selectCountByParentId(id) > 0) {
            throw exception(DEPT_EXITS_CHILDREN);
        }
        // 删除部门
        deptMapper.deleteById(id);
    }

    private void validateForCreateOrUpdate(Long id, Long parentId, String name) {
        // 校验自己存在
        validateDeptExists(id);
        // 校验父部门的有效性
        validateParentDept(id, parentId);
        // 校验部门名的唯一性
        validateDeptNameUnique(id, parentId, name);
    }

    @VisibleForTesting
    void validateDeptExists(Long id) {
        if (id == null) {
            return;
        }
        DeptDO dept = TenantUtils.executeIgnore(() -> {
            return deptMapper.selectById(id);
        });
        if (dept == null) {
            throw exception(DEPT_NOT_FOUND);
        }
    }

    @VisibleForTesting
    void validateParentDept(Long id, Long parentId) {
        if (parentId == null || DeptIdEnum.ROOT.getId().equals(parentId)) {
            return;
        }
        // 不能设置自己为父部门
        if (parentId.equals(id)) {
            throw exception(DEPT_PARENT_ERROR);
        }
        // 父岗位不存在
        DeptDO dept = TenantUtils.executeIgnore(() -> {
            return deptMapper.selectById(parentId);
        });
        if (dept == null) {
            throw exception(DEPT_PARENT_NOT_EXITS);
        }
        // 父部门不能是原来的子部门
        List<DeptDO> children = getChildDeptList(id);
        if (children.stream().anyMatch(dept1 -> dept1.getId().equals(parentId))) {
            throw exception(DEPT_PARENT_IS_CHILD);
        }
    }

    @VisibleForTesting
    void validateDeptNameUnique(Long id, Long parentId, String name) {
        DeptDO dept = TenantUtils.executeIgnore(() -> {
            return deptMapper.selectByParentIdAndName(parentId, name);
        });
        if (dept == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的岗位
        if (id == null) {
            throw exception(DEPT_NAME_DUPLICATE);
        }
        if (ObjectUtil.notEqual(dept.getId(), id)) {
            throw exception(DEPT_NAME_DUPLICATE);
        }
    }

    @Override
    @TenantIgnore
    public DeptDO getDept(Long id) {
        return deptMapper.selectById(id);
    }

    @Override
    @TenantIgnore
    public DeptDO getDept(String name) {
        List<DeptDO> deptList = deptMapper.selectList(new LambdaQueryWrapperX<DeptDO>().eq(DeptDO::getName, name));
        return deptList.isEmpty() ? null : deptList.get(0);
    }

    @Override
    public List<Long> getDeptKeyByAncestors(String ancestors) {
        final List<DeptDO> deptList = deptMapper.selectList(new LambdaQueryWrapperX<DeptDO>().
                likeRight(DeptDO::getAncestors, ancestors));
        return deptList.stream().map(DeptDO::getId).collect(Collectors.toList());
    }

    @Override
    @TenantIgnoreAdmin
    public List<DeptDO> getDeptList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return deptMapper.selectBatchIds(ids);
    }

    @Override
    public List<DeptDO> getBannerList(Collection<Long> bannerIds) {
        if (CollUtil.isEmpty(bannerIds)) {
            return Collections.emptyList();
        }
        return deptMapper.selectList(new LambdaQueryWrapperX<DeptDO>()
                .in(DeptDO::getId, bannerIds).orderByAsc(DeptDO::getSort));
    }

    @Override
    @TenantIgnoreAdmin
    public List<DeptDO> getDeptList(DeptListReqVO reqVO) {
        return deptMapper.selectList(reqVO);
    }

    @Override
    public List<DeptDO> getDeptList(String name, Long parentId) {
        return deptMapper.selectList(new LambdaQueryWrapperX<DeptDO>()
                .eqIfPresent(DeptDO::getName, name).eqIfPresent(DeptDO::getParentId, parentId)
                .orderByAsc(DeptDO::getSort)
        );
    }

    @Override
    public List<DeptDO> getDeptClassifiedList(Long tenantId) {
        return deptMapper.selectList(new LambdaQueryWrapperX<DeptDO>()
                .eq(DeptDO::getParentId, tenantId).eq(DeptDO::getTenantId, tenantId));
    }


    @Override
    public List<DeptDO> getChildDeptList(Long id) {
        List<DeptDO> children = new LinkedList<>();
        // 遍历每一层
        Collection<Long> parentIds = Collections.singleton(id);
        for (int i = 0; i < Short.MAX_VALUE; i++) { // 使用 Short.MAX_VALUE 避免 bug 场景下，存在死循环
            // 查询当前层，所有的子部门
            Collection<Long> finalParentIds = parentIds;
            List<DeptDO> depts = TenantUtils.executeIgnore(() -> {
                return deptMapper.selectListByParentId(finalParentIds);
            });
            // 1. 如果没有子部门，则结束遍历
            if (CollUtil.isEmpty(depts)) {
                break;
            }
            // 2. 如果有子部门，继续遍历
            children.addAll(depts);
            parentIds = convertSet(depts, DeptDO::getId);
        }
        return children;
    }

    @Override
    public List<DeptDO> getDeptListOfDistrict() {
        List<DeptDO> depts = TenantUtils.executeIgnore(() -> {
            LambdaQueryWrapperX<DeptDO> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eq(DeptDO::getId, 1);
            queryWrapperX.or(orQuery -> {
                orQuery.eq(DeptDO::getParentId, DeptIdEnum.DEPT_ROOT.getId());
                orQuery.ge(DeptDO::getId, 1000);
            });
            queryWrapperX.orderByAsc(DeptDO::getSort);
            return deptMapper.selectList(queryWrapperX);
        });
        return depts;
    }

    @Override
    @DataPermission(enable = false) // 禁用数据权限，避免简历不正确的缓存
    @Cacheable(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST, key = "#id")
    public Set<Long> getChildDeptIdListFromCache(Long id) {
        List<DeptDO> children = getChildDeptList(id);
        return convertSet(children, DeptDO::getId);
    }

    @Override
    @TenantIgnoreAdmin
    public void validateDeptList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得科室信息
        Map<Long, DeptDO> deptMap = getDeptMap(ids);
        // 校验
        ids.forEach(id -> {
            DeptDO dept = deptMap.get(id);
            if (dept == null) {
                throw exception(DEPT_NOT_FOUND);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(dept.getStatus())) {
                throw exception(DEPT_NOT_ENABLE, dept.getName());
            }
        });
    }

    @Override
    public DeptsAndUsersRespVO getDeptsAndUsersByParentId(Long parentId) {
        assert parentId == null : "parentId 为 null";
        List<DeptDO> depts = TenantUtils.executeIgnore(() ->
                deptMapper.selectListByParentId(Collections.singleton(parentId)));
        List<AdminUserDO> users = TenantUtils.executeIgnore(() ->
                adminUserMapper.selectListByDeptIds(Collections.singleton(parentId)));
        DeptsAndUsersRespVO vo = new DeptsAndUsersRespVO();
        if (CollUtil.isNotEmpty(depts)) {
            vo.setDepts(DeptConvert.INSTANCE.convertList02(depts));
        } else {
            vo.setDepts(new ArrayList<>());
        }
        if (CollUtil.isNotEmpty(users)) {
            vo.setUsers(UserConvert.INSTANCE.convertSimpleList(users));
        } else {
            vo.setUsers(new ArrayList<>());
        }
        List<DeptDO> titleDepts = new ArrayList<>();
        getAllParentDepts(parentId, titleDepts);
        if (CollUtil.isNotEmpty(titleDepts)) {
            vo.setTitleDepts(DeptConvert.INSTANCE.convertList02(titleDepts));
        } else {
            vo.setTitleDepts(new ArrayList<>());
        }
        return vo;
    }

    private void getAllParentDepts(Long parentId, List<DeptDO> list) {
        //TenantUtils.executeIgnore(() -> {
        DeptDO dept = getDept(parentId);
        if (dept != null) {
            list.add(0, dept);
            getAllParentDepts(dept.getParentId(), list);
        }
        //});
    }

    @Override
    public Integer getEmployedNumber() {
        return deptMapper.getEmployedNumber();
    }
}

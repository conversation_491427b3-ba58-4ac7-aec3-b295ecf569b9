package com.yutoudev.irrigation.module.system.controller.admin.appversion.vo;

import com.yutoudev.irrigation.module.system.dal.dataobject.appversion.AppVersionDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 *
 * APP版本信息list查询RequestVO
 * @description 管理后台-APP版本信息list查询RequestVO，参数和 AppVersionPageReqVO 是一致的
 * <AUTHOR>
 * @time 2023-09-13 18:07:30
 *
 */
@Data
public class AppVersionQueryReqVO extends QueryCriteria<AppVersionDO> {

    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 版本名称
     * 
     */
    private String versionName;

    /**
     * 版本值
     * 
     */
    private Integer versionNumber;

    /**
     * 更新内容
     * 
     */
    private String content;

    /**
     * 下载路径
     * 
     */
    private String url;

    /**
     * 文件 ID
     * 
     */
    private Long fileId;

    /**
     * 文件名
     * 
     */
    private String name;

    /**
     * 文件路径
     * 
     */
    private String path;

    /**
     * MD5Hash
     * 
     */
    private String md5;

    /**
     * 创建者
     * 
     */
    private String creator;

    /**
     * 创建时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    /**
     * 更新者
     * 
     */
    private String updater;

    /**
     * 更新时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 是否删除
     * 
     */
    private Boolean deleted;

}

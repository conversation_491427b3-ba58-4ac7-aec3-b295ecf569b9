package com.yutoudev.irrigation.archive.controller.admin.archivesinfo.vo;

import com.yutoudev.irrigation.archive.dal.dataobject.archivesfiles.ArchivesFilesDO;
import com.yutoudev.irrigation.archive.dal.dataobject.archivesinfo.ArchivesInfoDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 档案基本信息list查询RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-档案基本信息list查询RequestVO，参数和 ArchivesInfoPageReqVO 是一致的
 * @time 2024-07-11 22:54:37
 */
@Data
public class ArchivesInfoQueryReqVO extends QueryCriteria<ArchivesInfoDO> {

    /**
     * 主键
     */
    private Long id;

    /**
     * 题名
     */
    private String name;

    /**
     * 档号
     */
    private String boxCode;

    /**
     * 年度
     *
     * @mock （编制日期？）
     */
    private String year;

    /**
     * 档案编号
     */
    private String code;

    /**
     * 责任者
     */
    private String responsible;

    /**
     * 存放位置
     */
    private String storageLocation;

    /**
     * 归档日期
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date archiveTime;

    /**
     * 归档者
     */
    private String archiver;

    /**
     * 页数
     */
    private Integer pageCount;

    /**
     * 密级
     *
     * @mock （1：公开 2：内部 3：秘密 4：机密）
     */
    private Integer secretLevel;

    /**
     * 档案门类
     */
    private Integer type;

    /**
     * 附件
     */
    private List<ArchivesFilesDO> file;

    /**
     * 状态
     *
     * @mock (1待审批 2审批未通过 3已归档)
     */
    private Integer status;

    /**
     * 档案概述
     */
    private String descs;

    /**
     * 上架状态
     *
     * @mock (0 ： 未上架 1 ： 已上架 ）
     */
    private Integer groundingStatus;

    /**
     * 上架时间
     */
    private LocalDateTime groundingTime;

    /**
     * 保管期限
     */
    private LocalDateTime retentionPeriod;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 删除标志
     */
    private Boolean deleted;

    /**
     * 租户编号
     */
    private Long tenantId;

}

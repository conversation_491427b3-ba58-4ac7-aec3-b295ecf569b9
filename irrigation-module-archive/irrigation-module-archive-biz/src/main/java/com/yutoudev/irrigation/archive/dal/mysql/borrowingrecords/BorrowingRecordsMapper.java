package com.yutoudev.irrigation.archive.dal.mysql.borrowingrecords;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.yutoudev.irrigation.archive.controller.admin.borrowingrecords.vo.BorrowingRecordsPageReqVO;
import com.yutoudev.irrigation.archive.controller.admin.borrowingrecords.vo.BorrowingRecordsQueryReqVO;
import com.yutoudev.irrigation.archive.controller.admin.borrowingrecords.vo.BorrowingRecordsRespVO;
import com.yutoudev.irrigation.archive.dal.dataobject.borrowingrecords.BorrowingRecordsDO;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.mybatis.core.mapper.BaseMapperX;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 档案借阅记录 Mapper
 *
 * <AUTHOR>
 */

/**
 * 档案借阅记录Mapper
 *
 * <AUTHOR>
 * @description 管理后台-档案借阅记录MybatisPlus Mapper
 * @time 2024-07-11 23:11:23
 */
@Mapper
public interface BorrowingRecordsMapper extends BaseMapperX<BorrowingRecordsDO> {
    default PageResult<BorrowingRecordsDO> selectPage(BorrowingRecordsPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<BorrowingRecordsDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<BorrowingRecordsDO>() {
        };
        queryCriteriaWrapperBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        return selectPage(pageParam, queryCriteriaWrapperBuilder.getQueryWrapper());
    }

    default List<BorrowingRecordsDO> selectList(BorrowingRecordsQueryReqVO reqVO) {
        QueryCriteriaWrapperBuilder<BorrowingRecordsDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<BorrowingRecordsDO>() {
        };
        queryCriteriaWrapperBuilder.build(reqVO);
        return selectList(queryCriteriaWrapperBuilder.getQueryWrapper());
    }

   IPage<BorrowingRecordsRespVO> selectPage(PageDTO<BorrowingRecordsRespVO> tPageDTO,@Param("dto") BorrowingRecordsPageReqVO pageReqVO);

    List<BorrowingRecordsRespVO> selectPage(@Param("dto") BorrowingRecordsQueryReqVO queryReqVO);
}

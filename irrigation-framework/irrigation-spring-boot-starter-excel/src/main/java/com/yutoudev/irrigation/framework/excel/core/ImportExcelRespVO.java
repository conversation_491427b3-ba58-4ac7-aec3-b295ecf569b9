package com.yutoudev.irrigation.framework.excel.core;


import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * Excel导入ResponseVO
 *
 * @description 公共Excel导入响应VO
 * <AUTHOR>
 * @time 2021/3/27 16:17
 */
@Data
@Builder
public class ImportExcelRespVO {

    /**
     * 创建成功的序号
     * @description index 为excel序号，value
     */
    private List<ImportExcelResultVO> insertSuccess;

    /**
     * 更新成功的序号
     * @description index 为excel序号，value
     */
    private List<ImportExcelResultVO> updateSuccess;

    /**
     * 导入失败的序号集合
     * @description index 为excel序号，value 为失败原因
     */
    private List<ImportExcelResultVO> failures;

}

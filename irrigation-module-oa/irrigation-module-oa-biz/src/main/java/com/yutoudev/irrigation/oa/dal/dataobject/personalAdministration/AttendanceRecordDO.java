package com.yutoudev.irrigation.oa.dal.dataobject.personalAdministration;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 考勤记录 DO
 *
 * <AUTHOR>
 */
@TableName("oa_attendance_record")
@KeySequence("oa_attendance_record_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AttendanceRecordDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    
    /**
     * 人员编号
     */
    private Long staffId;
    
    /**
     * 上班时间
     */
    private LocalDateTime checkInTime;
    
    /**
     * 下班时间
     */
    private LocalDateTime checkOutTime;
    
    /**
     * 人员姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String staffName;
    
    /**
     * 工号（非数据库字段）
     */
    @TableField(exist = false)
    private String staffCode;

} 
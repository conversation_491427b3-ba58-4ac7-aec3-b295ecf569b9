package com.yutoudev.irrigation.oa.service.officialdocument;

import com.google.common.collect.Lists;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.test.core.ut.BaseDbUnitTest;
import com.yutoudev.irrigation.oa.controller.admin.officialdocument.vo.receive.OfficialDocumentReceiveCreateReqVO;
import com.yutoudev.irrigation.oa.controller.admin.officialdocument.vo.receive.OfficialDocumentReceivePageReqVO;
import com.yutoudev.irrigation.oa.controller.admin.officialdocument.vo.receive.OfficialDocumentReceiveQueryReqVO;
import com.yutoudev.irrigation.oa.controller.admin.officialdocument.vo.receive.OfficialDocumentReceiveUpdateReqVO;
import com.yutoudev.irrigation.oa.dal.dataobject.officialdocument.OfficialDocumentReceiveDO;
import com.yutoudev.irrigation.oa.dal.mysql.officialdocument.OfficialDocumentReceiveMapper;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import jakarta.annotation.Resource;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yutoudev.irrigation.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yutoudev.irrigation.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yutoudev.irrigation.framework.test.core.util.RandomUtils.*;
import static com.yutoudev.irrigation.oa.enums.ErrorCodeConstants.OFFICIAL_DOCUMENT_RECEIVE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link OfficialDocumentReceiveServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(OfficialDocumentReceiveServiceImpl.class)
public class OfficialDocumentReceiveServiceImplTest extends BaseDbUnitTest {

    @Resource
    private OfficialDocumentReceiveServiceImpl officialDocumentReceiveService;

    @Resource
    private OfficialDocumentReceiveMapper officialDocumentReceiveMapper;

    @Test
    public void testCreateSuccess() {
        // 准备参数
        OfficialDocumentReceiveCreateReqVO reqVO = randomPojo(OfficialDocumentReceiveCreateReqVO.class);

        // 调用
        Long id = officialDocumentReceiveService.create(reqVO);
        // 断言
        assertNotNull(id);
        // 校验记录的属性是否正确
        OfficialDocumentReceiveDO officialDocumentReceive = officialDocumentReceiveMapper.selectById(id);
        assertPojoEquals(reqVO, officialDocumentReceive);
    }

    @Test
    public void testCreateBatchSuccess() {
        // 准备参数
        List<OfficialDocumentReceiveCreateReqVO> list = randomPojoList(OfficialDocumentReceiveCreateReqVO.class);
        // 调用
        boolean isCreateSuccess = officialDocumentReceiveService.createBatch(list);
        // 断言
        assertTrue(isCreateSuccess);
        // 随机根据查询条件去查询一个出来比较
        OfficialDocumentReceiveCreateReqVO reqVO = list.get(RandomUtils.nextInt(0, 4));

        // 准备参数
        OfficialDocumentReceivePageReqVO queryVO = new OfficialDocumentReceivePageReqVO();
        queryVO.setStatus(reqVO.getStatus());
        queryVO.setWorkNumber(reqVO.getWorkNumber());
        queryVO.setYearNumber(reqVO.getYearNumber());
        queryVO.setPhaseNumber(reqVO.getPhaseNumber());
        queryVO.setTitle(reqVO.getTitle());
        queryVO.setCopies(reqVO.getCopies());
        queryVO.setEmergencyLevel(reqVO.getEmergencyLevel());
        queryVO.setType(reqVO.getType());
        queryVO.setDrafter(reqVO.getDrafter());
        queryVO.setIssuer(reqVO.getIssuer());
        queryVO.setContent(reqVO.getContent());
        queryVO.setSendUnit(reqVO.getSendUnit());
        // 调用
        PageResult<OfficialDocumentReceiveDO> pageResult = officialDocumentReceiveService.page(queryVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(reqVO, pageResult.getList().get(0));
    }

    @Test
    public void testUpdateSuccess() {
        // mock 数据
        OfficialDocumentReceiveDO dbOfficialDocumentReceive = randomPojo(OfficialDocumentReceiveDO.class);
        officialDocumentReceiveMapper.insert(dbOfficialDocumentReceive);// @Sql: 先插入出一条存在的数据
        // 准备参数
        OfficialDocumentReceiveUpdateReqVO reqVO = randomPojo(OfficialDocumentReceiveUpdateReqVO.class, o -> {
            o.setId(dbOfficialDocumentReceive.getId()); // 设置更新的 ID
        });

        // 调用
        officialDocumentReceiveService.update(reqVO);
        // 校验是否更新正确
        OfficialDocumentReceiveDO officialDocumentReceive = officialDocumentReceiveMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, officialDocumentReceive);
    }

    @Test
    public void testUpdateBatchSuccess() {
        // mock 数据
        OfficialDocumentReceiveDO dbOfficialDocumentReceiveA = randomPojo(OfficialDocumentReceiveDO.class);
        officialDocumentReceiveMapper.insert(dbOfficialDocumentReceiveA);// @Sql: 先插入出一条存在的数据

        OfficialDocumentReceiveDO dbOfficialDocumentReceiveB = randomPojo(OfficialDocumentReceiveDO.class);
        officialDocumentReceiveMapper.insert(dbOfficialDocumentReceiveB);// @Sql: 再插入出一条存在的数据

        // 准备参数
        OfficialDocumentReceiveUpdateReqVO reqVOA = randomPojo(OfficialDocumentReceiveUpdateReqVO.class, o -> {
            o.setId(dbOfficialDocumentReceiveA.getId()); // 设置更新的 ID
        });
        // 准备参数
        OfficialDocumentReceiveUpdateReqVO reqVOB = randomPojo(OfficialDocumentReceiveUpdateReqVO.class, o -> {
            o.setId(dbOfficialDocumentReceiveB.getId()); // 设置更新的 ID
        });

        // 调用
        List<OfficialDocumentReceiveUpdateReqVO> list = Lists.newArrayList(reqVOA, reqVOB);
        boolean updateSuccess = officialDocumentReceiveService.updateBatch(list);
        assertTrue(updateSuccess);
        // 校验是否更新正确
        OfficialDocumentReceiveDO officialDocumentReceiveA = officialDocumentReceiveMapper.selectById(reqVOA.getId()); // 获取最新的
        assertPojoEquals(reqVOA, officialDocumentReceiveA);

        OfficialDocumentReceiveDO officialDocumentReceiveB = officialDocumentReceiveMapper.selectById(reqVOB.getId()); // 获取最新的
        assertPojoEquals(reqVOB, officialDocumentReceiveB);
    }

    @Test
    public void testUpdateNotExists() {
        // 准备参数
        OfficialDocumentReceiveUpdateReqVO reqVO = randomPojo(OfficialDocumentReceiveUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> officialDocumentReceiveService.update(reqVO), OFFICIAL_DOCUMENT_RECEIVE_NOT_EXISTS);
    }

    @Test
    public void testDeleteSuccess() {
        // mock 数据
        OfficialDocumentReceiveDO dbOfficialDocumentReceive = randomPojo(OfficialDocumentReceiveDO.class);
        officialDocumentReceiveMapper.insert(dbOfficialDocumentReceive);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbOfficialDocumentReceive.getId();

        // 调用
        officialDocumentReceiveService.delete(id);
        // 校验数据不存在了
        assertNull(officialDocumentReceiveMapper.selectById(id));
    }

    @Test
    public void testDeleteBatchSuccess() {
        // mock 数据
        OfficialDocumentReceiveDO dbOfficialDocumentReceiveA = randomPojo(OfficialDocumentReceiveDO.class);
        officialDocumentReceiveMapper.insert(dbOfficialDocumentReceiveA);// @Sql: 先插入出一条存在的数据

        OfficialDocumentReceiveDO dbOfficialDocumentReceiveB = randomPojo(OfficialDocumentReceiveDO.class);
        officialDocumentReceiveMapper.insert(dbOfficialDocumentReceiveB);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long aId = dbOfficialDocumentReceiveA.getId();
        Long bId = dbOfficialDocumentReceiveB.getId();
        List<Long> ids = Lists.newArrayList(aId, bId);
        // 调用
        officialDocumentReceiveService.deleteBatch(ids);
        // 校验数据不存在了
        assertNull(officialDocumentReceiveMapper.selectById(aId));
        assertNull(officialDocumentReceiveMapper.selectById(bId));
    }

    @Test
    public void testDeleteNotExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> officialDocumentReceiveService.delete(id), OFFICIAL_DOCUMENT_RECEIVE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPage() {
        // mock 数据
        OfficialDocumentReceiveDO dbOfficialDocumentReceive = randomPojo(OfficialDocumentReceiveDO.class, o -> { // 等会查询到
            o.setStatus(null);
            o.setCreateTime(null);
            o.setWorkNumber(null);
            o.setYearNumber(null);
            o.setPhaseNumber(null);
            o.setTitle(null);
            o.setCopies(null);
            o.setEmergencyLevel(null);
            o.setType(null);
            o.setDrafter(null);
            o.setIssuer(null);
            o.setIssueTime(null);
            o.setReceiveTime(null);
            o.setContent(null);
            o.setSendUnit(null);
        });
        officialDocumentReceiveMapper.insert(dbOfficialDocumentReceive);
        // 测试 status 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setStatus(null)));
        // 测试 createTime 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setCreateTime(null)));
        // 测试 workNumber 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setWorkNumber(null)));
        // 测试 yearNumber 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setYearNumber(null)));
        // 测试 phaseNumber 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setPhaseNumber(null)));
        // 测试 title 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setTitle(null)));
        // 测试 copies 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setCopies(null)));
        // 测试 emergencyLevel 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setEmergencyLevel(null)));
        // 测试 type 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setType(null)));
        // 测试 drafter 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setDrafter(null)));
        // 测试 issuer 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setIssuer(null)));
        // 测试 issueTime 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setIssueTime(null)));
        // 测试 receiveTime 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setReceiveTime(null)));
        // 测试 content 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setContent(null)));
        // 测试 sendUnit 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setSendUnit(null)));
        // 准备参数
        OfficialDocumentReceivePageReqVO reqVO = new OfficialDocumentReceivePageReqVO();
        reqVO.setStatus(null);
        //reqVO.setBeginCreateTime(null);
        //reqVO.setEndCreateTime(null);
        reqVO.setCreateTime(null);
        reqVO.setWorkNumber(null);
        reqVO.setYearNumber(null);
        reqVO.setPhaseNumber(null);
        reqVO.setTitle(null);
        reqVO.setCopies(null);
        reqVO.setEmergencyLevel(null);
        reqVO.setType(null);
        reqVO.setDrafter(null);
        reqVO.setIssuer(null);
        //reqVO.setBeginIssueTime(null);
        //reqVO.setEndIssueTime(null);
        reqVO.setIssueTime(null);
        //reqVO.setBeginReceiveTime(null);
        //reqVO.setEndReceiveTime(null);
        reqVO.setReceiveTime(null);
        reqVO.setContent(null);
        reqVO.setSendUnit(null);

        // 调用
        PageResult<OfficialDocumentReceiveDO> pageResult = officialDocumentReceiveService.page(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbOfficialDocumentReceive, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetList() {
        // mock 数据
        OfficialDocumentReceiveDO dbOfficialDocumentReceive = randomPojo(OfficialDocumentReceiveDO.class, o -> { // 等会查询到
            o.setStatus(null);
            o.setCreateTime(null);
            o.setWorkNumber(null);
            o.setYearNumber(null);
            o.setPhaseNumber(null);
            o.setTitle(null);
            o.setCopies(null);
            o.setEmergencyLevel(null);
            o.setType(null);
            o.setDrafter(null);
            o.setIssuer(null);
            o.setIssueTime(null);
            o.setReceiveTime(null);
            o.setContent(null);
            o.setSendUnit(null);
        });
        officialDocumentReceiveMapper.insert(dbOfficialDocumentReceive);
        // 测试 status 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setStatus(null)));
        // 测试 createTime 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setCreateTime(null)));
        // 测试 workNumber 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setWorkNumber(null)));
        // 测试 yearNumber 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setYearNumber(null)));
        // 测试 phaseNumber 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setPhaseNumber(null)));
        // 测试 title 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setTitle(null)));
        // 测试 copies 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setCopies(null)));
        // 测试 emergencyLevel 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setEmergencyLevel(null)));
        // 测试 type 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setType(null)));
        // 测试 drafter 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setDrafter(null)));
        // 测试 issuer 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setIssuer(null)));
        // 测试 issueTime 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setIssueTime(null)));
        // 测试 receiveTime 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setReceiveTime(null)));
        // 测试 content 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setContent(null)));
        // 测试 sendUnit 不匹配
        officialDocumentReceiveMapper.insert(cloneIgnoreId(dbOfficialDocumentReceive, o -> o.setSendUnit(null)));
        // 准备参数
        OfficialDocumentReceiveQueryReqVO reqVO = new OfficialDocumentReceiveQueryReqVO();
        reqVO.setStatus(null);
        reqVO.setCreateTime(null);
        reqVO.setWorkNumber(null);
        reqVO.setYearNumber(null);
        reqVO.setPhaseNumber(null);
        reqVO.setTitle(null);
        reqVO.setCopies(null);
        reqVO.setEmergencyLevel(null);
        reqVO.setType(null);
        reqVO.setDrafter(null);
        reqVO.setIssuer(null);
        reqVO.setIssueTime(null);
        reqVO.setReceiveTime(null);
        reqVO.setContent(null);
        reqVO.setSendUnit(null);

        // 调用
        List<OfficialDocumentReceiveDO> list = officialDocumentReceiveService.getList(reqVO);
        // 断言
        assertEquals(1, list.size());
        assertPojoEquals(dbOfficialDocumentReceive, list.get(0));
    }

}

package com.yutoudev.irrigation.cms.controller.admin.article.vo;

import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 *
 * 文章DetailResponseVO
 * @description 管理后台-文章DetailResponseVO
 * <AUTHOR>
 * @time 2023-08-28 11:42:36
 *
 */
@Data
@ToString(callSuper = true)
public class ArticleDetailRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 列表显示类型
     * @mock 0：纯文本；1：一张大图；2：一张中图；3：三张小图
     */
    private Integer listType;

    /**
     * 文章类型
     * @mock 1：法律文件；2：政策文件；3：新闻资讯
     */
    private Integer type;

    /**
     * 文章标题
     * 
     */
    private String title;

    /**
     * 所属栏目ID
     * 
     */
    private String channelId;

    /**
     * 副标题
     * 
     */
    private String summary;

    /**
     * 关键字
     * 
     */
    private String keywords;

    /**
     * 内容
     * 
     */
    private String content;

    /**
     * 封面
     */
    private String cover;

    /**
     * 成文日期
     * 
     */
    private LocalDateTime finishDate;

    /**
     * 来源
     * 
     */
    private String source;

    /**
     * 作者
     * 
     */
    private String author;

    /**
     * 发文机关
     * 
     */
    private String organize;

    /**
     * 发文代字
     * 
     */
    private String docWords;

    /**
     * 年号
     * 
     */
    private Integer docYear;

    /**
     * 期号
     * 
     */
    private Integer docIssue;

    /**
     * 访问次数
     * 
     */
    private Integer clicks;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;
}

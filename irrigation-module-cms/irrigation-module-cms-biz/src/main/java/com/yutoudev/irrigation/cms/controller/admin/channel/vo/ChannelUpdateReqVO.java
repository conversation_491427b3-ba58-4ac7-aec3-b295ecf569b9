package com.yutoudev.irrigation.cms.controller.admin.channel.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

/**
 *
 * 文章栏目更新RequestVO
 * @description 管理后台-文章栏目更新RequestVO
 * <AUTHOR>
 * @time 2023-08-22 15:07:31
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelUpdateReqVO extends ChannelBaseVO {

    /**
     * ID
     * 
     */
    @NotNull(message = "ID不能为空")
    private Long id;
    /**
     * 父栏目ID
     * 
     */
    private Long parentId;
}
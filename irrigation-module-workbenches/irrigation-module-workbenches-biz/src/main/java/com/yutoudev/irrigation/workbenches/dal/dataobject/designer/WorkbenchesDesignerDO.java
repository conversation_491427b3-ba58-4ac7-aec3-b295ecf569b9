package com.yutoudev.irrigation.workbenches.dal.dataobject.designer;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 工作台设计DO
 *
 * <AUTHOR>
 */
@TableName("workbenches_designer")
@KeySequence("workbenches_designer_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorkbenchesDesignerDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 工作台配置JSON
     */
    private String configJson;

    /**
     * 是否公共配置
     */
    private Boolean publiced;

    /**
     * 用户ID
     */
    private Long userId;
} 
package com.yutoudev.irrigation.workbenches.controller.admin.vo.designer;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 工作台设计创建请求VO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class WorkbenchesDesignerCreateReqVO {

    /**
     * 工作台配置JSON
     */
    @NotEmpty(message = "工作台配置不能为空")
    private String configJson;

    /**
     * 是否公共配置
     */
    private Boolean publiced;

    /**
     * 用户ID
     */
    private Long userId;
} 
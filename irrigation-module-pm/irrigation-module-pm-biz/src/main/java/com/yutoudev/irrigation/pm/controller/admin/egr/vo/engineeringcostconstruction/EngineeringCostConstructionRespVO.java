package com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringcostconstruction;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 工程造价ResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-工程造价ResponseVO
 * @time 2024-07-21 22:41:36
 */
@Data
@ToString(callSuper = true)
public class EngineeringCostConstructionRespVO {


    /**
     * 工程信息主键
     */
    private String engineeringInfoId;
    private String engineeringInfoName;

    /**
     * 费用项名称
     */
    private String name;

    /**
     * 工程量
     */
    private Double schedule;

    /**
     * 工程量单位
     */
    private String scheduleUnit;

    /**
     * 造价单方指标
     */
    private Double costUnilateralIndicator;

    /**
     * 造价单方指标单位
     */
    private String costUnilateralIndicatorUnit;

    /**
     * 结算金额
     */
    private Double settlementAmount;

    /**
     * 占工程费用比例
     */
    private Double proportion;

    /**
     * 备注
     */
    private String remark;

    /**
     * ID
     */
    private Long id;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 删除标志
     */
    private Boolean deleted;

    /**
     * 租户编号
     */
    private Long tenantId;
}
package com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringcontract;

import com.yutoudev.irrigation.pm.dal.dataobject.egr.EngineeringFilesDO;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 工程合同信息 Base VO
 *
 * <AUTHOR>
 * @description 工程合同信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-07-21 22:37:40
 */
@Data
public class EngineeringContractBaseVO {

    /**
     * 工程信息主键
     */
    private Long engineeringInfoId;

    /**
     * 合同名称
     */
    private String name;

    /**
     * 编号
     */
    private String code;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 签订日期
     */
    private LocalDate signingDate;

    /**
     * 甲方
     */
    private String firstParty;

    /**
     * 甲方负责人
     */
    private String firstPartyCommander;

    /**
     * 甲方法人
     */
    private String firstPartyCorporation;

    /**
     * 甲方负责人联系方式
     */
    private String firstPartyCommanderPhone;

    /**
     * 乙方
     */
    private String secondParty;

    /**
     * 乙方负责人
     */
    private String secondPartyCommander;

    /**
     * 乙方法人
     */
    private String secondPartyCorporation;

    /**
     * 乙方负责人联系方式
     */
    private String secondPartyCommanderPhone;

    /**
     * 金额
     */
    private String amount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 合同主要内容
     */
    private String mainContent;

    /**
     * 付款条件
     */
    private String paymentTerms;

    /**
     * 附件信息
     */
    private List<EngineeringFilesDO> files;
}

package com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringinfo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Date;

/**
 * 工程信息ExcelVO
 *
 * <AUTHOR>
 * @description 管理后台-工程信息导出、导入ExcelVO
 * @time 2024-07-22 09:41:37
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class EngineeringInfoExcelVO {


    /**
     * ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 工程名称
     */
    @ExcelProperty("工程名称")
    private String name;

    /**
     * 工程编号
     */
    @ExcelProperty("工程编号")
    private String code;

    /**
     * 工程类型
     */
    @ExcelProperty("工程类型")
    private String type;

    /**
     * 申请单位
     */
    @ExcelProperty("申请单位")
    private String applyUnit;

    /**
     * 计划开工日期
     */
    @ExcelProperty("计划开工日期")
    private LocalDate startTimePlan;

    /**
     * 计划竣工日期
     */
    @ExcelProperty("计划竣工日期")
    private LocalDate endTimePlan;

    /**
     * 规模
     */
    @ExcelProperty("规模")
    private Integer cale;

    /**
     * 经费来源
     */
    @ExcelProperty("经费来源")
    private Integer sourcesFunds;

    /**
     * 负责人
     */
    @ExcelProperty("负责人")
    private String commander;

    /**
     * 负责人手机号
     */
    @ExcelProperty("负责人手机号")
    private String commanderPhone;

    /**
     * 施工地点
     */
    @ExcelProperty("施工地点")
    private String constructionSite;

    /**
     * 简介
     */
    @ExcelProperty("简介")
    private String briefIntroduction;

    /**
     * 背景
     */
    @ExcelProperty("背景")
    private String background;

    /**
     * 施工单位
     */
    @ExcelProperty("施工单位")
    private String constructionUnit;

    /**
     * 施工单位联系方式
     */
    @ExcelProperty("施工单位联系方式")
    private String constructionUnitPhone;

    /**
     * 施工单位负责人
     */
    @ExcelProperty("施工单位负责人")
    private String constructionUnitCommander;

    /**
     * 施工单位负责人联系方式
     */
    @ExcelProperty("施工单位负责人联系方式")
    private String constructionUnitCommanderPhone;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    /**
     * 状态
     */
    @ExcelProperty("状态")
    @ColumnWidth(20)
    private String status;
}

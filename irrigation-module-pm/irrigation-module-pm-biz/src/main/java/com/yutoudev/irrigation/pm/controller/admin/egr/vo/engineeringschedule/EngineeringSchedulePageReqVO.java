package com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringschedule;

import com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringunit.EngineeringUnitRespVO;
import com.yutoudev.irrigation.pm.dal.dataobject.egr.EngineeringScheduleDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 工程进度分页RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-工程进度分页RequestVO
 * @time 2024-07-22 10:23:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EngineeringSchedulePageReqVO extends PageCriteria<EngineeringScheduleDO> {

    /**
     * 工程信息主键
     */
    private Long engineeringInfoId;

    /**
     * 工程计划项主键
     */
    private Long planId;

    /**
     * 备注
     */
    private String remark;


    private List<EngineeringUnitRespVO> unitList;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

}

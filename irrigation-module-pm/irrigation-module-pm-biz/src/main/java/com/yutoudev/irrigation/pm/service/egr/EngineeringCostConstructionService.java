package com.yutoudev.irrigation.pm.service.egr;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringcostconstruction.*;
import com.yutoudev.irrigation.pm.dal.dataobject.egr.EngineeringCostConstructionDO;
import io.github.portaldalaran.talons.core.ITalonsService;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 工程造价Service接口
 *
 * <AUTHOR>
 * @description 管理后台-工程造价Service接口，对ITalonsService的扩展
 * @time 2024-07-21 22:41:36
 */
public interface EngineeringCostConstructionService<T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建工程造价
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid EngineeringCostConstructionCreateReqVO createReqVO);

    /**
     * 批量创建工程造价
     *
     * @param list 创建信息
     * @return 编号
     */
    boolean createBatch(@Valid List<EngineeringCostConstructionCreateReqVO> list);

    /**
     * 更新工程造价
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid EngineeringCostConstructionUpdateReqVO updateReqVO);

    /**
     * 批量更新工程造价
     *
     * @param list 更新信息
     */
    boolean updateBatch(@Valid List<EngineeringCostConstructionUpdateReqVO> list);

    /**
     * 删除工程造价
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
     * 批量删除工程造价
     *
     * @param ids 编号
     */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得工程造价
     *
     * @param id 编号
     * @return 工程造价
     */
    EngineeringCostConstructionDO get(Long id);

    /**
     * 工程造价列表
     *
     * @param ids 编号
     * @return 工程造价列表
     */
    List<EngineeringCostConstructionDO> getList(List<Long> ids);

    /**
     * 工程造价分页
     *
     * @param pageReqVO 分页查询
     * @return 工程造价分页
     */
    PageResult<EngineeringCostConstructionDO> page(EngineeringCostConstructionPageReqVO pageReqVO);

    /**
     * 获得工程造价列表,
     *
     * @param queryReqVO 查询条件键值对
     * @return 工程造价列表
     */
    List<EngineeringCostConstructionDO> getList(EngineeringCostConstructionQueryReqVO queryReqVO);

    /**
     * 批量导入工程造价 excel
     *
     * @param importList      导入工程造价列表
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    ImportExcelRespVO importExcel(List<EngineeringCostConstructionExcelVO> importList, boolean isUpdateSupport);
}
package com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringplan;

import com.yutoudev.irrigation.pm.dal.dataobject.egr.EngineeringPlanDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 工程计划分页RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-工程计划分页RequestVO
 * @time 2024-07-22 10:20:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EngineeringPlanPageReqVO extends PageCriteria<EngineeringPlanDO> {

    /**
     * 工程信息主键
     */
    private String engineeringInfoId;

    /**
     * 工作项名称
     */
    private String name;

    /**
     * 计划开始时间
     */
    private LocalDate planStartTime;

    /**
     * 计划结束时间
     */
    private LocalDate planEndTime;

    /**
     * 实际开始时间
     */
    private LocalDate startTime;

    /**
     * 实际结束时间
     */
    private LocalDate endTime;

    /**
     * 状态
     *
     * @mock （0未开始 1进行中 2已完成）
     */
    private Integer status;

    /**
     * 负责人
     */
    private String commander;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

}

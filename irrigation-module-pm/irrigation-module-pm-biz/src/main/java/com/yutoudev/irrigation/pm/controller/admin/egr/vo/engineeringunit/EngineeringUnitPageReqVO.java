package com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringunit;

import com.yutoudev.irrigation.pm.dal.dataobject.egr.EngineeringUnitDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 *
 * 工程量分页RequestVO
 * @description 管理后台-工程量分页RequestVO
 * <AUTHOR>
 * @time 2024-07-22 20:58:22
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EngineeringUnitPageReqVO extends PageCriteria<EngineeringUnitDO> {

    /**
     * 工程信息主键
     * 
     */
    private String engineeringInfoId;

    /**
     * 施工阶段
     * 
     */
    private String stage;

    /**
     * 施工子项
     * 
     */
    private String childStage;

    /**
     * 负责人
     * 
     */
    private String commander;

    /**
     * 计划开始时间
     * 
     */
    private LocalDate planStartTime;

    /**
     * 计划结束时间
     * 
     */
    private LocalDate planEndTime;

    /**
     * 实际开始时间
     * 
     */
    private LocalDate startTime;

    /**
     * 实际结束时间
     * 
     */
    private LocalDate endTime;

    /**
     * 状态
     * @mock （0未开始 1进行中 2已完成）
     */
    private Integer status;

    /**
     * 工程量
     * 
     */
    private Double amount;

    /**
     * 工程量单位
     * 
     */
    private String amountUnit;

    /**
     * 工程计划主键
     * 
     */
    private Long planId;

    /**
     * 创建时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

}

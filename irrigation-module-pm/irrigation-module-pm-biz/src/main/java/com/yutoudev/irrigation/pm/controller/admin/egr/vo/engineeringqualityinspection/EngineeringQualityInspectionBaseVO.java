package com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringqualityinspection;

import com.yutoudev.irrigation.pm.dal.dataobject.egr.EngineeringFilesDO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工程质量检查 Base VO
 *
 * <AUTHOR>
 * @description 工程质量检查 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-07-22 10:21:27
 */
@Data
public class EngineeringQualityInspectionBaseVO {

    /**
     * 工程信息主键
     */
    private String engineeringInfoId;

    /**
     * 检查日期
     */
    private LocalDateTime inspectionDate;

    /**
     * 检查人
     */
    private String inspectionPerson;

    /**
     * 检查项
     */
    private String inspectionItem;

    /**
     * 是否合格
     */
    private Integer qualified;

    /**
     * 是否整改
     */
    private Integer rectification;

    /**
     * 整改期限
     */
    private LocalDateTime rectificationDeadline;

    /**
     * 状态
     */
    private Integer status;


    /**
     * 附件信息
     */
    private List<EngineeringFilesDO> files;
}

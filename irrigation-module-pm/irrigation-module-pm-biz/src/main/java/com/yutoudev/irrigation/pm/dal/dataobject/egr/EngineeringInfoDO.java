package com.yutoudev.irrigation.pm.dal.dataobject.egr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 工程信息DO
 *
 * <AUTHOR>
 * @description 管理后台-工程信息数据库对象
 * @time 2024-07-22 09:41:37
 */
@TableName(value = "pm_engineering_info", autoResultMap = true)
@KeySequence("pm_engineering_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EngineeringInfoDO extends BaseDO {

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 工程名称
     */
    private String name;

    /**
     * 工程编号
     */
    private String code;

    /**
     * 工程类型
     */
    private String type;

    /**
     * 申请单位
     */
    private String applyUnit;

    /**
     * 计划开工日期
     */
    private LocalDate startTimePlan;

    /**
     * 计划竣工日期
     */
    private LocalDate endTimePlan;

    /**
     * 规模
     */
    private Integer cale;

    /**
     * 经费来源
     */
    private Integer sourcesFunds;

    /**
     * 负责人
     */
    private String commander;

    /**
     * 负责人手机号
     */
    private String commanderPhone;

    /**
     * 施工地点
     */
    private String constructionSite;

    /**
     * 简介
     */
    private String briefIntroduction;

    /**
     * 背景
     */
    private String background;

    /**
     * 施工单位
     */
    private String constructionUnit;

    /**
     * 施工单位联系方式
     */
    private String constructionUnitPhone;

    /**
     * 施工单位负责人
     */
    private String constructionUnitCommander;

    /**
     * 施工单位负责人联系方式
     */
    private String constructionUnitCommanderPhone;

    /**
     * 状态
     */
    private String status;


}

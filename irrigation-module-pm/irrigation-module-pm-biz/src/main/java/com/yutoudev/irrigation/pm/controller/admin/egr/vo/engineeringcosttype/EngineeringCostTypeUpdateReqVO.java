package com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringcosttype;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;


/**
 * 工程量成本类型更新RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-工程量成本类型更新RequestVO
 * @time 2024-07-21 22:41:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EngineeringCostTypeUpdateReqVO extends EngineeringCostTypeBaseVO {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}
package com.yutoudev.irrigation.pm.controller.admin.egr;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.pm.controller.admin.egr.vo.engineeringfiles.*;
import com.yutoudev.irrigation.pm.convert.egr.EngineeringFilesConvert;
import com.yutoudev.irrigation.pm.dal.dataobject.egr.EngineeringFilesDO;
import com.yutoudev.irrigation.pm.service.egr.EngineeringFilesService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 工程文件信息
 *
 * <AUTHOR>
 * @description 管理后台-工程文件信息controller
 * @time 2024-07-21 22:59:39
 */
@RestController
@RequestMapping("/pm/engineering-files")
@Validated
public class EngineeringFilesController {

    private static final String MODULE_NAME = "工程文件信息";

    @Resource
    private EngineeringFilesService<EngineeringFilesDO> engineeringFilesService;

    /**
     * 创建工程文件信息
     *
     * @param createReqVO EngineeringFilesCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody EngineeringFilesCreateReqVO createReqVO) {
        return success(engineeringFilesService.create(createReqVO));
    }

    /**
     * 批量创建工程文件信息
     *
     * @param lists EngineeringFilesCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<EngineeringFilesCreateReqVO> lists) {
        return success(engineeringFilesService.createBatch(lists));
    }

    /**
     * 更新工程文件信息
     *
     * @param updateReqVO EngineeringFilesUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody EngineeringFilesUpdateReqVO updateReqVO) {
        engineeringFilesService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新工程文件信息
     *
     * @param lists 批量更新列表 EngineeringFilesUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<EngineeringFilesUpdateReqVO> lists) {
        return success(engineeringFilesService.updateBatch(lists));
    }

    /**
     * 删除工程文件信息
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        engineeringFilesService.delete(id);
        return success(true);
    }

    /**
     * 批量删除工程文件信息
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(engineeringFilesService.deleteBatch(ids));
    }

    /**
     * 获得工程文件信息详情
     *
     * @param id 编号 Long
     * @return CommonResult<EngineeringFilesDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<EngineeringFilesDetailRespVO> get(@RequestParam("id") Long id) {
        EngineeringFilesDO engineeringFiles = engineeringFilesService.get(id);
        // 获取邀请单位信息

        // 获取投标单位信息
        return success(EngineeringFilesConvert.INSTANCE.convertDetail(engineeringFiles));
    }

    /**
     * 工程文件信息列表
     *
     * @param queryReqVO 查询条件 EngineeringFilesQueryReqVO
     * @return CommonResult<List < EngineeringFilesRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<EngineeringFilesRespVO>> getList(@RequestQueryParam EngineeringFilesQueryReqVO queryReqVO) {
        List<EngineeringFilesDO> list = engineeringFilesService.getList(queryReqVO);
        return success(EngineeringFilesConvert.INSTANCE.convertList(list));
    }

    /**
     * 工程文件信息分页
     *
     * @param pageVO 查询条件 EngineeringFilesPageReqVO
     * @return CommonResult<PageResult < EngineeringFilesRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<EngineeringFilesRespVO>> page(@RequestQueryParam EngineeringFilesPageReqVO pageVO) {
        PageResult<EngineeringFilesDO> pageResult = engineeringFilesService.page(pageVO);
        return success(EngineeringFilesConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出工程文件信息Excel
     *
     * @param queryReqVO 查询条件 EngineeringFilesExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam EngineeringFilesExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<EngineeringFilesDO> list = engineeringFilesService.getList(queryReqVO);
        // 导出 Excel
        List<EngineeringFilesExcelVO> datas = EngineeringFilesConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "工程文件信息", "xlsx"), queryReqVO.getExportSheetName(),
                EngineeringFilesExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入工程文件信息模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "工程文件信息-导入模版.xls", "sheet1", EngineeringFilesExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入工程文件信息Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('pm:engineering-files:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<EngineeringFilesExcelVO> list = ExcelUtils.read(file, EngineeringFilesExcelVO.class);
        return success(engineeringFilesService.importExcel(list, isUpdate));
    }
}
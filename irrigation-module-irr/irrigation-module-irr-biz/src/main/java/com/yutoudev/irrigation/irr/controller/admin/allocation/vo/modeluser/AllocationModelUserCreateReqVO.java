package com.yutoudev.irrigation.irr.controller.admin.allocation.vo.modeluser;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;


/**
 * 用水单元优化模型创建RequestVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AllocationModelUserCreateReqVO extends AllocationModelUserBaseVO {

    /**
     * 明细
     */
    private List<AllocationModelUserItemReqVO> items;
}

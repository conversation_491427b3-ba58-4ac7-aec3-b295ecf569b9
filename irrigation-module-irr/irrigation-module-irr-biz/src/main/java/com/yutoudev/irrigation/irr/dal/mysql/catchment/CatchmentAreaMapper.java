package com.yutoudev.irrigation.irr.dal.mysql.catchment;

import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.mybatis.core.mapper.BaseMapperX;
import com.yutoudev.irrigation.irr.controller.admin.catchment.vo.catchmentarea.CatchmentAreaPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.catchment.vo.catchmentarea.CatchmentAreaQueryReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.catchment.CatchmentAreaDO;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 集水面积 Mapper
 *
 * <AUTHOR>
 */
/**
 *
 * 集水面积Mapper
 * @description 管理后台-集水面积MybatisPlus Mapper
 * <AUTHOR>
 * @time 2024-07-09 23:34:12
 *
 */
@Mapper
public interface CatchmentAreaMapper extends BaseMapperX<CatchmentAreaDO> {
    default PageResult<CatchmentAreaDO> selectPage(CatchmentAreaPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<CatchmentAreaDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<CatchmentAreaDO>(){};
        queryCriteriaWrapperBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        return selectPage(pageParam, queryCriteriaWrapperBuilder.getQueryWrapper());
    }

    default List<CatchmentAreaDO> selectList(CatchmentAreaQueryReqVO reqVO) {
        QueryCriteriaWrapperBuilder<CatchmentAreaDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<CatchmentAreaDO>(){};
        queryCriteriaWrapperBuilder.build(reqVO);
        return selectList(queryCriteriaWrapperBuilder.getQueryWrapper());
    }

    /**
     * 根据集水面积ID 修改集水面积数
     * @param id
     * @return
     */
    int updateAreaById(@Param("id")Long id);

}

package com.yutoudev.irrigation.irr.controller.admin.viewtemplate.vo.item;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * 参数显示配置模版明细创建RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-参数显示配置模版明细创建RequestVO
 * @time 2024-06-07 15:17:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ViewTemplateItemCreateReqVO extends ViewTemplateItemBaseVO {

}

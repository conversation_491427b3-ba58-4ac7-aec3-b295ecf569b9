package com.yutoudev.irrigation.irr.controller.admin.history.vo.historycapacityday;

import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryCapacityDayDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;

import java.time.LocalDate;


/**
 * 历史每日库容量list查询RequestVO
 *
 * <AUTHOR>
 */
@Data
public class HistoryCapacityDayQueryReqVO extends QueryCriteria<HistoryCapacityDayDO> {

    /**
     * ID
     */
    private Long id;

    /**
     * 日期
     */
    private LocalDate markDay;

    /**
     * 水源地ID
     */
    private Long swhsId;

    /**
     * 水源地名称
     */
    private String swhsName;

    /**
     * 库容量
     */
    private Double capacity;

}

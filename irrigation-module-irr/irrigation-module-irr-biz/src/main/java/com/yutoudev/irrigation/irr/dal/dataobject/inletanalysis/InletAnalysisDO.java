package com.yutoudev.irrigation.irr.dal.dataobject.inletanalysis;

import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import com.yutoudev.irrigation.irr.dal.mysql.swhsbase.SwhsBaseMapper;
import io.github.portaldalaran.talons.annotation.ManyToOne;
import lombok.*;
import java.util.*;
import java.time.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;

/**
 *
 * 来水分析DO
 * @description 管理后台-来水分析数据库对象
 * <AUTHOR>
 * @time 2024-06-16 20:28:54
 *
 */
@TableName(value = "irr_inlet_analysis", autoResultMap = true)
@KeySequence("irr_inlet_analysis_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InletAnalysisDO extends BaseDO {

    /**
     * ID
     * 
     */
    @TableId
    private Long id;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水源名称
     * 
     */
    private String swhsName;

    /**
     * 水渠ID
     *
     */
    private Long chanId;

    /**
     * 水渠名称
     *
     */
    private String chanName;


    /**
     * 统计日期
     * 
     */
    private String statisticsDate;

    /**
     * 日期类型
     * 
     */
    private Integer dateType;

    /**
     * 进水量
     * 
     */
    private Double inflow;

    /**
     * 降水量
     * 
     */
    private Double rainfall;

    /**
     * 蒸发量
     * 
     */
    private Double evaporation;

    /**
     * 损耗值
     */
    private Double loss;

    /**
     * 渗透值
     */
    private Double penetration;

    /**
     * 平均进水量
     *
     */
    private Double avgInflow;

    /**
     * 平均降水量
     *
     */
    private Double avgRainfall;

    /**
     * 平均损耗值
     */
    private Double avgLoss;

    /**
     * 平均渗透值
     */
    private Double avgPenetration;

    /**
     * 平均蒸发量
     *
     */
    private Double avgEvaporation;


    @TableField(exist = false)
    private String creator;

    @TableField(exist = false)
    private String updater;

    @TableField(exist = false)
    private Boolean deleted;
}

package com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class MeasIndHourRainfallSimpleRespVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 站点ID
     */
    private Long equipId;

    /**
     * 小时时间
     */
    private LocalDateTime hourTime;

    /**
     * 雨量
     */
    private Float rainfallVolume;
}

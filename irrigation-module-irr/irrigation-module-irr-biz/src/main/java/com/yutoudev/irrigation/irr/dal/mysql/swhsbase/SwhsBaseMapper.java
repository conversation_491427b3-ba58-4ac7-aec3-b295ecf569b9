package com.yutoudev.irrigation.irr.dal.mysql.swhsbase;

import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.mybatis.core.mapper.BaseMapperX;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base.SwhsBaseQueryReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.swhsbase.SwhsBaseDO;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 水源地基础信息 Mapper
 *
 * <AUTHOR>
 */

/**
 * 水源地基础信息Mapper
 *
 * <AUTHOR>
 * @description 管理后台-水源地基础信息MybatisPlus Mapper
 * @time 2024-05-11 09:46:40
 */
@Mapper
public interface SwhsBaseMapper extends BaseMapperX<SwhsBaseDO> {
    default PageResult<SwhsBaseDO> selectPage(SwhsBasePageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<SwhsBaseDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<SwhsBaseDO>() {
        };
        queryCriteriaWrapperBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        return selectPage(pageParam, queryCriteriaWrapperBuilder.getQueryWrapper());
    }

    default List<SwhsBaseDO> selectList(SwhsBaseQueryReqVO reqVO) {
        QueryCriteriaWrapperBuilder<SwhsBaseDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<SwhsBaseDO>() {
        };
        queryCriteriaWrapperBuilder.build(reqVO);
        return selectList(queryCriteriaWrapperBuilder.getQueryWrapper());
    }

}

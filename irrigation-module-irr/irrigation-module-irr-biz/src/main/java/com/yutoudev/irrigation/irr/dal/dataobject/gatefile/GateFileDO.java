package com.yutoudev.irrigation.irr.dal.dataobject.gatefile;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 *
 * 闸控设备上传照片文件DO
 * @description 管理后台-闸控设备上传照片文件数据库对象
 * <AUTHOR>
 * @time 2024-05-21 10:42:01
 *
 */
@TableName(value = "irr_gate_file", autoResultMap = true)
@KeySequence("irr_gate_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GateFileDO extends BaseDO {

    /**
     * 文件编号
     * 
     */
    @TableId
    private Long id;

    /**
     * 中心站ID
     *
     */
    private String centralId;

    /**
     * 设备ID
     * 
     */
    private String devId;

    /**
     * 文件名
     * 
     */
    private String name;

    /**
     * 文件路径
     * 
     */
    private String path;

    /**
     * 文件 URL
     * 
     */
    private String url;

    /**
     * 文件类型
     * 
     */
    private String type;

    /**
     * 文件后缀
     * 
     */
    private String suffix;

    /**
     * 文件大小
     * 
     */
    private Integer size;

    /**
     * MD5
     * 
     */
    private String md5;


}

package com.yutoudev.irrigation.irr.controller.admin.inletanalysisday.vo;

import lombok.*;

import java.util.*;
import java.time.*;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.*;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 日来水分析ResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-日来水分析ResponseVO
 * @time 2024-07-23 14:33:36
 */
@Data
@ToString(callSuper = true)
public class InletAnalysisDayRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 集水面积ID
     */
    private Long catchmentId;
    /**
     * 集水面积名称
     */
    private String catchmentName;

    /**
     * 水源ID
     */
    private Long swhsId;
    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;
    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 日
     */
    private String day;

    /**
     * 累计降雨量
     */
    private Double rainfall;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
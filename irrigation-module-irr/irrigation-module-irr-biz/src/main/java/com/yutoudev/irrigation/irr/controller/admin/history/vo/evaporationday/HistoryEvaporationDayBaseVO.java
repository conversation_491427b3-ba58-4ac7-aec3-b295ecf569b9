package com.yutoudev.irrigation.irr.controller.admin.history.vo.evaporationday;

import lombok.Data;

import java.time.LocalDate;

/**
 * 历史日均蒸散发量 Base VO
 *
 * <AUTHOR>
 * @description 历史日均蒸散发量 Base VO，提供给添加、修改、详细的子 VO 使用
 * @time 2024-12-20 19:58:36
 */
@Data
public class HistoryEvaporationDayBaseVO {

    /**
     * 时间
     */
    private LocalDate markTime;

    /**
     * 水源地ID
     */
    private Long swhsId;

    /**
     * 水源地名称
     */
    private String swhsName;

    /**
     * 水面蒸发值
     */
    private Double evaporationValue;
}

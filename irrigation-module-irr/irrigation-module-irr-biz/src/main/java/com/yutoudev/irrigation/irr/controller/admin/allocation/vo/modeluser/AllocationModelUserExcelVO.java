package com.yutoudev.irrigation.irr.controller.admin.allocation.vo.modeluser;

import lombok.*;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.experimental.Accessors;

/**
 *
 * 用水单元优化模型ExcelVO
 * @description 管理后台-用水单元优化模型导出、导入ExcelVO
 * <AUTHOR>
 * @time 2024-11-30 10:09:06
 *
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class AllocationModelUserExcelVO {


    /**
     * ID
     * 
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 模型名称
     * 
     */
    @ExcelProperty("模型名称")
    private String name;

    /**
     * 模型编号
     */
    @ExcelProperty("模型编号")
    private String code;

}

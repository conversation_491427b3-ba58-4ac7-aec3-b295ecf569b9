package com.yutoudev.irrigation.irr.util.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryCapacityDO;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryCapacityDayDO;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryInflowDO;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryInflowDayDO;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 导出库容量量数据工具类
 */
public class CapacityExportUtils {
    public static <T> void write(HttpServletResponse response, String templatePath,
                                 HistoryCapacityDO capacityYear,
                                 List<HistoryCapacityDO> capacityMonthList,
                                 List<HistoryCapacityDayDO> capacityDayList) throws IOException {


        Map<String, Object> yearData = yearDataFill(capacityYear);
        List<Map<String, Object>> dayFillList = dayDataFill(capacityDayList, capacityMonthList, capacityYear);

        String filename = MessageFormat.format("{0}{1}年水库逐日库容量表.xlsx", capacityYear.getSwhsName(), capacityYear.getCode());

        // 设置 header 和 contentType。写在最后的原因是，避免报错时，响应 contentType 已经被修改了
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(templatePath).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.VERTICAL).build();

            //先写列表
            excelWriter.fill(new FillWrapper("day", dayFillList), fillConfig, writeSheet);
            excelWriter.fill(yearData, writeSheet);
        }

    }

    private static Map<String, Object> yearDataFill(HistoryCapacityDO capacityYear) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("code", capacityYear.getCode());
        resultMap.put("name", capacityYear.getName());
        resultMap.put("swhsName", capacityYear.getSwhsName());
        resultMap.put("total", capacityYear.getTotal());
        resultMap.put("maxVolume", capacityYear.getMaxVolume());
        resultMap.put("maxDay", capacityYear.getMaxDay().format(DateTimeFormatter.ofPattern("M月d日")));
        resultMap.put("minVolume", capacityYear.getMinVolume());
        resultMap.put("minDay", capacityYear.getMinDay().format(DateTimeFormatter.ofPattern("M月d日")));
        resultMap.put("avgVolume", capacityYear.getAvgVolume());

        resultMap.put("remark", capacityYear.getRemark());
        resultMap.put("lister", capacityYear.getLister());
        if (Objects.nonNull(capacityYear.getMarkTime())) {
            resultMap.put("markTime", capacityYear.getMarkTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")));
        } else {
            resultMap.put("markTime", " 年  月  日");
        }
        return resultMap;
    }

    private static List<Map<String, Object>> dayDataFill(List<HistoryCapacityDayDO> capacityDayList, List<HistoryCapacityDO> capacityMonthList, HistoryCapacityDO capacityYear) {
        List<Map<String, Object>> dayFillList = new ArrayList<>();
        Object[][] dayData = new Object[38][13];
        for (HistoryCapacityDayDO capacityDay : capacityDayList) {
            int month = capacityDay.getMarkDay().getMonthValue();
            int day = capacityDay.getMarkDay().getDayOfMonth();
            dayData[day][month] = capacityDay.getCapacity();
        }
        /**
         * 32 总数
         * 33 平均
         * 34 最大
         * 35 日期
         * 36 最小
         * 37 日期
         */
        for (HistoryCapacityDO month : capacityMonthList) {
            int monthIndex = (int) (month.getCode() - capacityYear.getCode() * 100);
            //月总量
            dayData[32][monthIndex] = month.getTotal();
            //平均
            dayData[33][monthIndex] = month.getAvgVolume();
            //最大来水量
            dayData[34][monthIndex] = month.getMaxVolume();
            //日期
            dayData[35][monthIndex] = month.getMaxDay().getDayOfMonth();
            //最大来水量
            dayData[36][monthIndex] = month.getMinVolume();
            //日期
            dayData[37][monthIndex] = month.getMinDay().getDayOfMonth();
        }

        for (int i = 1; i < dayData.length; i++) {
            Map<String, Object> fillMap = new HashMap<>();
            fillMap.put("code", i);
            fillMap.put("m1", dayData[i][1]);
            fillMap.put("m2", dayData[i][2]);
            fillMap.put("m3", dayData[i][3]);
            fillMap.put("m4", dayData[i][4]);
            fillMap.put("m5", dayData[i][5]);
            fillMap.put("m6", dayData[i][6]);
            fillMap.put("m7", dayData[i][7]);
            fillMap.put("m8", dayData[i][8]);
            fillMap.put("m9", dayData[i][9]);
            fillMap.put("m10", dayData[i][10]);
            fillMap.put("m11", dayData[i][11]);
            fillMap.put("m12", dayData[i][12]);
            dayFillList.add(fillMap);
        }
        return dayFillList;
    }


}

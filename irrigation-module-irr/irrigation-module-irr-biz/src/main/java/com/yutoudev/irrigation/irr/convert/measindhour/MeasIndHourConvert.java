package com.yutoudev.irrigation.irr.convert.measindhour;

import java.util.*;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;

import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;

/**
 *
 * 量测设备指标统计小时Convert
 * @description 管理后台-量测设备指标统计小时 mapstruct Convert
 * <AUTHOR>
 * @time 2024-07-27 15:15:00
 *
 */
@Mapper
public interface MeasIndHourConvert {

    MeasIndHourConvert INSTANCE = Mappers.getMapper(MeasIndHourConvert.class);

    /**
    * CreateReqVO转为DO
    * @param createReqVO createReqVO
    * @return DO
    */
    MeasIndHourDO convert(MeasIndHourCreateReqVO createReqVO);

    /**
    * 批量把 CreateReqVO 转为 DO
    * @param list CreateReqVO list
    * @return DO list
    */
    List<MeasIndHourDO> convertCreateBatch(List<MeasIndHourCreateReqVO> list);

    /**
    * UpdateReqVO转为DO
    * @param updateReqVO updateReqVO
    * @return DO
    */
    MeasIndHourDO convert(MeasIndHourUpdateReqVO updateReqVO);

    /**
    * 批量把 UpdateReqVO转为DO
    * @param list UpdateReqVO list
    * @return DO
    */
    List<MeasIndHourDO> convertUpdateBatch(List<MeasIndHourUpdateReqVO> list);

    /**
    * DO转化为RespVO
    * @param bean DO
    * @return RespVO
    */
    MeasIndHourRespVO convert(MeasIndHourDO bean);

    /**
    * DO转化为 DetailRespVO
    * @param bean DO
    * @return DetailRespVO
    */
    MeasIndHourDetailRespVO convertDetail(MeasIndHourDO bean);

    /**
    * DO转化为 RainfallSimpleRespVO
    * @param bean DO
    * @return RainfallSimpleRespVO
    */
    MeasIndHourRainfallSimpleRespVO convertRainfallSimple(MeasIndHourDO bean);

    /**
    * 批量把DO转化为RespVO
    * @param list DO list
    * @return RespVO list
    */
    List<MeasIndHourRespVO> convertList(List<MeasIndHourDO> list);

    /**
    * 批量把DO转化为RainfallSimpleRespVO
    * @param list DO list
    * @return RainfallSimpleRespVO list
    */
    List<MeasIndHourRainfallSimpleRespVO> convertRainfallSimpleList(List<MeasIndHourDO> list);

    /**
    * 把PageResult中的DO转化为RespVO
    * @param page PageResult DO
    * @return PageResult RespVO
    */
    PageResult<MeasIndHourRespVO> convertPage(PageResult<MeasIndHourDO> page);

    /**
    * 批量把DO转化为ExcelVO
    * @param list DO list
    * @return ExcelVO list
    */
    List<MeasIndHourExcelVO> convertExportExcel(List<MeasIndHourDO> list);

    /**
    * 批量把ExcelVO转化为DO
    * @param list ExcelVO list
    * @return DO list
    */
    List<MeasIndHourDO> convertImportExcel(List<MeasIndHourExcelVO> list);
}

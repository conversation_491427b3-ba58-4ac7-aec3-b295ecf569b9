package com.yutoudev.irrigation.irr.service.waterflowconfig;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.irr.controller.admin.waterflowconfig.vo.config.*;
import com.yutoudev.irrigation.irr.convert.waterflowconfig.WaterFlowConfigConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.waterflowconfig.WaterFlowConfigDO;
import com.yutoudev.irrigation.irr.dal.dataobject.waterflowconfig.WaterFlowConfigInfoDO;
import com.yutoudev.irrigation.irr.dal.mysql.waterflowconfig.WaterFlowConfigMapper;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.ErrorCodeConstants.*;

/**
 * 水流配置Service实现类
 *
 * <AUTHOR>
 * @description 管理后台-水流配置Service实现类
 * @time 2024-08-13 15:15:33
 */
@Service
@Validated
public class WaterFlowConfigServiceImpl extends TalonsServiceImpl<WaterFlowConfigMapper, WaterFlowConfigDO> implements WaterFlowConfigService<WaterFlowConfigDO> {

    @Resource
    private WaterFlowConfigMapper waterFlowConfigMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Resource
    private WaterFlowConfigInfoService waterFlowConfigInfoService;

    @Override
    public void checkField(WaterFlowConfigDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(WaterFlowConfigCreateReqVO createReqVO) {
        // 插入
        WaterFlowConfigDO waterFlowConfig = WaterFlowConfigConvert.INSTANCE.convert(createReqVO);
        this.save(waterFlowConfig, true);
        // 返回
        return waterFlowConfig.getId();
    }

    @Override
    public boolean createBatch(List<WaterFlowConfigCreateReqVO> list) {

        List<WaterFlowConfigDO> saveList = WaterFlowConfigConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        } else {
            throw exception(WATER_FLOW_CONFIG_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public void update(WaterFlowConfigUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateWaterFlowConfigExists(updateReqVO.getId());
        // 更新
        WaterFlowConfigDO updateObj = WaterFlowConfigConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    public boolean updateBatch(List<WaterFlowConfigUpdateReqVO> list) {

        List<WaterFlowConfigDO> updateList = WaterFlowConfigConvert.INSTANCE.convertUpdateBatch(list);

        for (WaterFlowConfigDO tempDO : updateList) {
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateWaterFlowConfigExists(tempDO.getId());
        }

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(WATER_FLOW_CONFIG_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    @Transactional
    public void delete(Long id) {
        // 校验存在
        this.validateWaterFlowConfigExists(id);
        // 删除
        this.removeById(id, true);

        // 同时将该水流配置下信息全部删除
        QueryWrapper<WaterFlowConfigInfoDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("water_flow_id", id);
        waterFlowConfigInfoService.remove(queryWrapper);
    }

    @Override
    @Transactional
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            // 同时将该水流配置下信息全部删除
            QueryWrapper<WaterFlowConfigInfoDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("water_flow_id", ids);
            waterFlowConfigInfoService.remove(queryWrapper);
            return true;
        } else {
            throw exception(WATER_FLOW_CONFIG_DELETE_BATCH_ERROR);
        }
    }

    private void validateWaterFlowConfigExists(Long id) {
        if (waterFlowConfigMapper.selectById(id) == null) {
            throw exception(WATER_FLOW_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public WaterFlowConfigDO get(Long id) {
        return this.getById(id, true);
    }

    @Override
    public List<WaterFlowConfigDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public PageResult<WaterFlowConfigDO> page(WaterFlowConfigPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<WaterFlowConfigDO> queryBuilder = new QueryCriteriaWrapperBuilder<WaterFlowConfigDO>() {
        };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<WaterFlowConfigDO> pageResult = waterFlowConfigMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
        talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<WaterFlowConfigDO> getList(WaterFlowConfigQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<WaterFlowConfigDO> queryBuilder = new QueryCriteriaWrapperBuilder<WaterFlowConfigDO>() {
        };
        queryBuilder.build(queryReqVO);

        List<WaterFlowConfigDO> result = waterFlowConfigMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<WaterFlowConfigExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(WATER_FLOW_CONFIG_IMPORT_LIST_IS_EMPTY);
        }

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<WaterFlowConfigDO> saveList = WaterFlowConfigConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            WaterFlowConfigDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, WaterFlowConfigExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}

package com.yutoudev.irrigation.irr.service.map;

import com.yutoudev.irrigation.irr.controller.admin.map.vo.trajectory.*;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import java.util.*;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;

import com.yutoudev.irrigation.irr.dal.dataobject.map.MapTrajectoryDO;
import com.yutoudev.irrigation.irr.convert.map.MapTrajectoryConvert;
import com.yutoudev.irrigation.irr.dal.mysql.map.MapTrajectoryMapper;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.ErrorCodeConstants.*;

/**
 *
 * 地图轨迹Service实现类
 * @description 管理后台-地图轨迹Service实现类
 * <AUTHOR>
 * @time 2025-01-15 09:38:05
 *
 */
@Service
@Validated
public class MapTrajectoryServiceImpl extends TalonsServiceImpl<MapTrajectoryMapper,MapTrajectoryDO> implements MapTrajectoryService<MapTrajectoryDO> {

    @Resource
    private MapTrajectoryMapper mapTrajectoryMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Override
    public void checkField(MapTrajectoryDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(MapTrajectoryCreateReqVO createReqVO) {
        // 插入
        MapTrajectoryDO mapTrajectory = MapTrajectoryConvert.INSTANCE.convert(createReqVO);
        this.save(mapTrajectory, true);
        // 返回
        return mapTrajectory.getId();
    }

    @Override
    public boolean createBatch(List<MapTrajectoryCreateReqVO> list) {

        List<MapTrajectoryDO> saveList = MapTrajectoryConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        }else{
            throw exception(MAP_TRAJECTORY_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public void update(MapTrajectoryUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateMapTrajectoryExists(updateReqVO.getId());
        // 更新
        MapTrajectoryDO updateObj = MapTrajectoryConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    public boolean updateBatch(List<MapTrajectoryUpdateReqVO> list) {

        List<MapTrajectoryDO> updateList = MapTrajectoryConvert.INSTANCE.convertUpdateBatch(list);

        for(MapTrajectoryDO tempDO: updateList){
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateMapTrajectoryExists(tempDO.getId());
        }

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(MAP_TRAJECTORY_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateMapTrajectoryExists(id);
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(MAP_TRAJECTORY_DELETE_BATCH_ERROR);
        }
    }

    private void validateMapTrajectoryExists(Long id) {
        if (mapTrajectoryMapper.selectById(id) == null) {
            throw exception(MAP_TRAJECTORY_NOT_EXISTS);
        }
    }

    @Override
    public MapTrajectoryDO get(Long id) {
        return this.getById(id, true);
    }
    @Override
    public List<MapTrajectoryDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public PageResult<MapTrajectoryDO> page(MapTrajectoryPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<MapTrajectoryDO> queryBuilder = new QueryCriteriaWrapperBuilder<MapTrajectoryDO>() { };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<MapTrajectoryDO> pageResult = mapTrajectoryMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
            talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<MapTrajectoryDO> getList(MapTrajectoryQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<MapTrajectoryDO> queryBuilder = new QueryCriteriaWrapperBuilder<MapTrajectoryDO>() { };
        queryBuilder.build(queryReqVO);

        List<MapTrajectoryDO> result = mapTrajectoryMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<MapTrajectoryExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(MAP_TRAJECTORY_IMPORT_LIST_IS_EMPTY);
        }

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<MapTrajectoryDO> saveList = MapTrajectoryConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            MapTrajectoryDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, MapTrajectoryExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}

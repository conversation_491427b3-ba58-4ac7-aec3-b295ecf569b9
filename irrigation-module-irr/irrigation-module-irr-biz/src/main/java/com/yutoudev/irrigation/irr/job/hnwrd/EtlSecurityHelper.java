package com.yutoudev.irrigation.irr.job.hnwrd;

import org.apache.commons.codec.digest.DigestUtils;

import java.nio.charset.StandardCharsets;

/**
 * 湖南省厅上报数据，加密相关的工具类
 *
 * <AUTHOR>
 */
public class EtlSecurityHelper {

    public static String sign(String data, SignTypeEnum signType, String secret) {
        if (signType == SignTypeEnum.MD5) {
            data = data + secret;
            return DigestUtils.md5Hex(data.getBytes(StandardCharsets.UTF_8)).toUpperCase();
        }
        throw new RuntimeException("不支持的签名类型：" + signType.getValue());
    }

    /**
     * 生成一个 APP SECRET
     * @return APP SECRET
     */
    public static String generateAppSecret() {
        return MyIdentities.randomNumAndLowercase(32);
    }

    public static void main(String[] args) {
        System.out.println(generateAppSecret());
    }
}

package com.yutoudev.irrigation.irr.service.datarecovery;

import com.yutoudev.irrigation.irr.controller.admin.measrealtime.vo.MeasRealtimeUpdateReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.catchment.CatchmentAreaEquipDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measanalysisday.MeasAnalysisDayDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.*;
import com.yutoudev.irrigation.irr.dal.dataobject.measrealtime.MeasRealtimeDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measreport.MeasReportDO;
import com.yutoudev.irrigation.irr.service.catchment.CatchmentAreaEquipService;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.measanalysisday.MeasAnalysisDayService;
import com.yutoudev.irrigation.irr.service.measind.*;
import com.yutoudev.irrigation.irr.service.measrealtime.MeasRealtimeService;
import com.yutoudev.irrigation.irr.service.measreport.MeasReportService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;
import static com.yutoudev.irrigation.irr.enums.BasicSetErrorCodeConstants.EQUIP_BASE_NOT_EXISTS;
import static com.yutoudev.irrigation.irr.mq.MqConfigConstants.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RainfallDataRecoveryServiceImpl implements RainfallDataRecoveryService {

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    @Resource
    private MeasReportService<MeasReportDO> measReportService;

    @Resource
    private MeasRealtimeService<MeasRealtimeDO> measRealtimeService;

    @Resource
    private MeasIndMinuteService<MeasIndMinuteDO> measIndMinuteService;

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndHourService;

    @Resource
    private MeasIndDayService<MeasIndDayDO> measIndDayService;

    @Resource
    private MeasIndMonthService<MeasIndMonthDO> measIndMonthService;

    @Resource
    private MeasIndYearService<MeasIndYearDO> measIndYearService;

    @Resource
    private CatchmentAreaEquipService<CatchmentAreaEquipDO> catchmentAreaEquipService;

    @Resource
    private MeasAnalysisDayService<MeasAnalysisDayDO> measAnalysisDayService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoveryDataByMonth(Long equipId, Integer year, Integer month) {
        LocalDateTime statTime = LocalDateTime.now();
        LocalDateTime calcMonth = LocalDateTime.of(year, month, 1, 0, 0, 0);

        if (calcMonth.isAfter(statTime)) {
            return;
        }

        EquipBaseDO equip = equipBaseService.getById(equipId, false);
        if (Objects.isNull(equip)) {
            throw exception(EQUIP_BASE_NOT_EXISTS);
        }

        Integer rainfallAlgorithm = Optional.ofNullable(equip.getRainfallAlgorithm()).orElse(MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE);

        // 查询原始上报数据
        LocalDateTime beginTime = calcMonth.withHour(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endTime = calcMonth.withMonth(month).plusMonths(1).withHour(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).withMinute(0).withSecond(0).withNano(0);
        List<MeasReportDO> allDataList = measReportService.getListByEquipAndDateRange(equip, rainfallAlgorithm, beginTime, endTime);

        if (CollectionUtils.isEmpty(allDataList)) {
            return;
        }

        // 处理分钟数据
        List<MeasIndMinuteDO> createMinuteList = assembleMinuteList(equip, rainfallAlgorithm, beginTime, endTime, allDataList, statTime);

        if (!CollectionUtils.isEmpty(createMinuteList)) {
            measIndMinuteService.saveBatch(createMinuteList);
        }

        List<MeasIndHourDO> createHourList = assembleHourList(equip, beginTime, endTime, allDataList, statTime);
        if (!CollectionUtils.isEmpty(createHourList)) {
            measIndHourService.saveBatch(createHourList);
        }

        // 处理日数据
        List<MeasIndDayDO> createDayList = assembleDayList(equip, calcMonth, beginTime, endTime, statTime);
        if (!CollectionUtils.isEmpty(createDayList)) {
            measIndDayService.saveBatch(createDayList);
        }

        // 处理月数据
        List<MeasIndMonthDO> createMonthList = assembleMonthList(equip, calcMonth, statTime);
        if (!CollectionUtils.isEmpty(createMonthList)) {
            measIndMonthService.saveBatch(createMonthList);
        }

        // 处理年数据
        List<MeasIndYearDO> createYearList = assembleYearList(year, equip, statTime);
        if (!CollectionUtils.isEmpty(createYearList)) {
            measIndYearService.saveBatch(createYearList);
        }

        // 处理实时数据
        if (Objects.equals(statTime.getYear(), year) && Objects.equals(statTime.getMonthValue(), month) ) {
            MeasRealtimeDO realtime = measRealtimeService.getByCentralIdAndDevId(equip.getCentralId(), equip.getDevId());
            MeasRealtimeUpdateReqVO update = new MeasRealtimeUpdateReqVO();
            update.setId(realtime.getId());
            if (!CollectionUtils.isEmpty(createDayList)) {
                List<MeasIndDayDO> daySortedList = createDayList.stream().sorted(Comparator.comparing(MeasIndDayDO::getDay)).toList();
                if (Objects.equals(daySortedList.get(0).getDay(), statTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY)))) {
                    update.setDayRainfall(daySortedList.get(0).getRainfallVolume());
                }
            }
            if (!CollectionUtils.isEmpty(createMonthList)) {
                update.setMonthRainfall(createMonthList.get(0).getRainfallVolume());
            }
            update.setUpdateTime(statTime);
            measRealtimeService.update(update);
        }

        // 处理集水区站占降雨平均值
        List<MeasAnalysisDayDO> createAnalysisList = assembleAnalysisList(equip, calcMonth, statTime);
        if (!CollectionUtils.isEmpty(createAnalysisList)) {
            measAnalysisDayService.saveBatch(createAnalysisList);
        }
    }

    @NotNull
    private List<MeasAnalysisDayDO> assembleAnalysisList(EquipBaseDO equip, LocalDateTime calcMonth, LocalDateTime statTime) {
        // 根据站点ID获取所属集水区域
        CatchmentAreaEquipDO areaEquip = catchmentAreaEquipService.getByEquipId(equip.getId());
        // 根据集水区域ID获取集水面积设备列表
        List<CatchmentAreaEquipDO> areaEquips = catchmentAreaEquipService.getListByAreaId(areaEquip.getCatchmentId());
        // 将areaEquips转换为map
        Map<Long, CatchmentAreaEquipDO> areaMap = areaEquips.stream().collect(Collectors.toMap(CatchmentAreaEquipDO::getEquipId, Function.identity()));

        List<Long> ids = areaEquips.stream().map(CatchmentAreaEquipDO::getEquipId).toList();
        // 获取当前已有数据
        List<MeasAnalysisDayDO> monthDayList = measAnalysisDayService.getListByCatchmentAndMonth(areaEquip.getCatchmentId(), calcMonth.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH)));

        // 根据设备ID列表获取雨量日表数据
        List<MeasIndDayDO> dayEquipsList = measIndDayService.getListByEquipIdsAndDayRange(ids, calcMonth.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH)));

        Map<String, List<MeasIndDayDO>> dayMap = dayEquipsList.stream().collect(Collectors.groupingBy(MeasIndDayDO::getDay));
        List<MeasAnalysisDayDO> createAnalysisList = new LinkedList<>();
        for (Map.Entry<String, List<MeasIndDayDO>> entry : dayMap.entrySet()) {
            String day = entry.getKey();
            List<MeasIndDayDO> dayList = entry.getValue();
            MeasAnalysisDayDO analysis;
            Optional<MeasAnalysisDayDO> optional = monthDayList.stream().filter(measAnalysisDayDO -> measAnalysisDayDO.getDay().equals(day)).findFirst();
            analysis = optional.orElseGet(MeasAnalysisDayDO::new);
            analysis.setCatchmentId(areaEquip.getCatchmentId());
            analysis.setDay(day);
            analysis.setEquipCount(dayList.size());

            double avgRainfall = dayList.stream().mapToDouble(MeasIndDayDO::getRainfallVolume).average().orElse(0.0);
            analysis.setAvgRainfall(convert(avgRainfall, 1).floatValue());
            double weightedRainfallSum = 0d;
            List<CatchmentAreaEquipDO> filterAreaEquips = areaEquips.stream().filter(e -> dayList.stream().anyMatch(d -> d.getEquipId().equals(e.getEquipId()))).toList();
            double areaSum = filterAreaEquips.stream().mapToDouble(CatchmentAreaEquipDO::getArea).sum();
            for (MeasIndDayDO equipDayData: dayList) {
                CatchmentAreaEquipDO area = areaMap.get(equipDayData.getEquipId());
                weightedRainfallSum += equipDayData.getRainfallVolume() * (area.getArea() / areaSum); ;

            }
            float weightedAvg = convert(weightedRainfallSum, 1).floatValue();
            analysis.setWeightedAvgRainfall(weightedAvg);
            analysis.setUpdateTime(statTime);
            createAnalysisList.add(analysis);
        }

        createAnalysisList.sort(Comparator.comparing(MeasAnalysisDayDO::getDay));

        return createAnalysisList;
    }

    @NotNull
    private List<MeasIndYearDO> assembleYearList(Integer year, EquipBaseDO equip, LocalDateTime statTime) {

        List<MeasIndMonthDO> monthDataList = measIndMonthService.getRainfallSumByYear(equip, year.toString());
        double sumData = monthDataList.stream().mapToDouble(MeasIndMonthDO::getRainfallVolume).sum();

        MeasIndYearDO yearData = measIndYearService.getByEquipAndYear(equip, year.toString());

        List<MeasIndYearDO> createYearList = new LinkedList<>();
        MeasIndYearDO createYear;
        if (!Objects.isNull(yearData)) {
            createYear = yearData;
            createYear.setRainfallVolume((float) sumData);
            createYear.setUpdateTime(statTime);
        } else {
            createYear = new MeasIndYearDO();
            createYear.setEquipId(equip.getId());
            createYear.setCentralId(equip.getCentralId());
            createYear.setDevId(equip.getDevId());
            createYear.setYear(year.toString());
            createYear.setRainfallVolume((float) sumData);
            createYear.setUpdateTime(statTime);
        }
        createYearList.add(createYear);
        return createYearList;
    }

    @NotNull
    private List<MeasIndMonthDO> assembleMonthList(EquipBaseDO equip, LocalDateTime calcMonth, LocalDateTime statTime) {
        MeasIndMonthDO monthData = measIndMonthService.getByEquipAndMonth(equip, calcMonth.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH)));
        List<MeasIndDayDO> dayDataList = measIndDayService.getListByEquipAndDayRange(equip, calcMonth.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH)));

        if (CollectionUtils.isEmpty(dayDataList)) {
            return Collections.emptyList();
        }

        double monthSum = dayDataList.stream().mapToDouble(MeasIndDayDO::getRainfallVolume).sum();
        List<MeasIndMonthDO> createMonthList = new LinkedList<>();
        MeasIndMonthDO createMonth;
        if (!Objects.isNull(monthData)) {
            createMonth = monthData;
        } else {
            createMonth = new MeasIndMonthDO();
            createMonth.setEquipId(equip.getId());
            createMonth.setCentralId(equip.getCentralId());
            createMonth.setDevId(equip.getDevId());
            createMonth.setMonth(calcMonth.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH)));
        }
        createMonth.setRainfallVolume((float) monthSum);
        createMonth.setUpdateTime(statTime);
        createMonthList.add(createMonth);
        return createMonthList;
    }

    @NotNull
    private List<MeasIndDayDO> assembleDayList(EquipBaseDO equip, LocalDateTime calcMonth, LocalDateTime beginTime, LocalDateTime endTime, LocalDateTime statTime) {
        List<MeasIndDayDO> dayDataList = measIndDayService.getListByEquipAndDayRange(equip, calcMonth.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH)));
        List<MeasIndHourDO> hourDataList = measIndHourService.getListByEquipAndDateRange(equip, beginTime, endTime);

        if (CollectionUtils.isEmpty(hourDataList)) {
            return Collections.emptyList();
        }

        List<MeasIndDayDO> calcDayList = new LinkedList<>();
        for (MeasIndHourDO hour : hourDataList) {
            MeasIndDayDO day = new MeasIndDayDO();
            LocalDateTime hourTime = hour.getHourTime();
            if (hourTime.getHour() > MEAS_DAY_WATERFALL_HOUR_DIFFERENCE) {
                day.setDay(hourTime.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY)));
            } else {
                day.setDay(hourTime.minusDays(1).format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY)));
            }
            day.setEquipId(equip.getId());
            day.setCentralId(equip.getCentralId());
            day.setDevId(equip.getDevId());
            day.setRainfallVolume(hour.getRainfallVolume());
            day.setUpdateTime(statTime);
            calcDayList.add(day);
        }

        Map<String, Double> collect = calcDayList.stream().collect(Collectors.groupingBy(MeasIndDayDO::getDay, Collectors.summingDouble(MeasIndDayDO::getRainfallVolume)));

        List<MeasIndDayDO> createDayList = new LinkedList<>();
        for (String key : collect.keySet()) {
            MeasIndDayDO createDay;
            Optional<MeasIndDayDO> optional = dayDataList.stream().filter(x -> x.getDay().equals(key)).findFirst();
            createDay = optional.orElseGet(MeasIndDayDO::new);
            createDay.setEquipId(equip.getId());
            createDay.setCentralId(equip.getCentralId());
            createDay.setDevId(equip.getDevId());
            createDay.setDay(key);
            createDay.setRainfallVolume(collect.get(key).floatValue());
            createDay.setUpdateTime(statTime);
            createDayList.add(createDay);
        }
        createDayList.sort(Comparator.comparing(MeasIndDayDO::getDay));
        return createDayList;
    }

    @NotNull
    private List<MeasIndHourDO> assembleHourList(EquipBaseDO equip, LocalDateTime beginTime, LocalDateTime endTime, List<MeasReportDO> allDataList, LocalDateTime statTime) {
        // 处理小时数据
        List<MeasIndHourDO> hourDataList = measIndHourService.getListByEquipAndDateRange(equip, beginTime, endTime);
        List<MeasIndHourDO> calcHourList = new LinkedList<>();
        for (MeasReportDO report : allDataList) {
            MeasIndHourDO hour = fillHourData(report);
            calcHourList.add(hour);
        }

        Map<LocalDateTime, Double> hourSumMap = calcHourList.stream().collect(Collectors.groupingBy(MeasIndHourDO::getHourTime, Collectors.summingDouble(MeasIndHourDO::getRainfallVolume)));

        List<MeasIndHourDO> createHourList = new LinkedList<>();
        for (LocalDateTime hourTime : hourSumMap.keySet()) {
            MeasIndHourDO createHour;
            Optional<MeasIndHourDO> optional = hourDataList.stream().filter(x -> x.getHourTime().equals(hourTime)).findFirst();
            createHour = optional.orElseGet(MeasIndHourDO::new);
            createHour.setCentralId(equip.getCentralId());
            createHour.setDevId(equip.getDevId());
            createHour.setEquipId(equip.getId());
            createHour.setRainfallVolume(hourSumMap.get(hourTime).floatValue());
            createHour.setHourTime(hourTime);
            createHour.setUpdateTime(statTime);
            createHourList.add(createHour);
        }
        createHourList.sort(Comparator.comparing(MeasIndHourDO::getHourTime));
        return createHourList;
    }

    @NotNull
    private static MeasIndHourDO fillHourData(MeasReportDO report) {
        MeasIndHourDO hour = new MeasIndHourDO();
        // 计算小时刻度
        LocalDateTime reportTime = report.getReportTime();
        if (reportTime.getMinute() == 0) {
            hour.setHourTime(report.getReportTime().withMinute(0).withSecond(0).withNano(0));
        } else {
            hour.setHourTime(reportTime.withHour(reportTime.getHour()).plusHours(1).withMinute(0).withSecond(0).withNano(0));
        }

        hour.setRainfallVolume(Float.parseFloat(report.getTypeValue()));
        return hour;
    }

    @NotNull
    private List<MeasIndMinuteDO> assembleMinuteList(EquipBaseDO equip, Integer rainfallAlgorithm, LocalDateTime beginTime, LocalDateTime endTime, List<MeasReportDO> allDataList, LocalDateTime statTime) {

        if (Objects.equals(rainfallAlgorithm, MEAS_INDICATES_TYPE_RAIN_HOUR)) {
            return Collections.emptyList();
        }

        List<MeasIndMinuteDO> minuteDataList = measIndMinuteService.getListByEquipAndDateRange(equip, beginTime, endTime);

        List<MeasIndMinuteDO> createMinuteList = new LinkedList<>();
        for (MeasReportDO report : allDataList) {
            MeasIndMinuteDO createMinute;
            Optional<MeasIndMinuteDO> optional = minuteDataList.stream().filter(x -> x.getReportTime().equals(report.getReportTime())).findFirst();
            createMinute = optional.orElseGet(MeasIndMinuteDO::new);
            createMinute.setCentralId(report.getCentralId());
            createMinute.setDevId(report.getDevId());
            createMinute.setEquipId(equip.getId());
            createMinute.setRainfall(Float.parseFloat(report.getTypeValue()));
            createMinute.setReportTime(report.getReportTime());
            createMinute.setUpdateTime(statTime);
            createMinuteList.add(createMinute);
        }
        return createMinuteList;
    }
}

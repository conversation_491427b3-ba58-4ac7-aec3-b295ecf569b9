package com.yutoudev.irrigation.irr.controller.admin.plan.vo.droughtplan;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * 抗旱预案更新RequestVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DroughtPlanUpdateReqVO extends DroughtPlanBaseVO {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Long id;
}
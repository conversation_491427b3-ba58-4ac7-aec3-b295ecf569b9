package com.yutoudev.irrigation.irr.controller.admin.flood.vo.ooddischargeflowcurve;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 下泄流量关系曲线ExcelVO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(20)
public class DischargeFlowCurveExcelVO {
    /**
     * 水位
     */
    @ExcelProperty("水位")
    private Double waterLevel;

    /**
     * 库容
     */
    @ExcelProperty("库容")
    private Double capacity;

    /**
     * 总下泄流量
     */
    @ExcelProperty("总下泄流量")
    private Double totalDischargeFlow;
}

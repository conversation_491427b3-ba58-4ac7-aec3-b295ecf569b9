package com.yutoudev.irrigation.irr.controller.admin.inletanalysisday.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisday.InletAnalysisDayDO;

/**
 *
 * 日来水分析分页RequestVO
 * @description 管理后台-日来水分析分页RequestVO
 * <AUTHOR>
 * @time 2024-07-23 14:33:36
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisDayPageReqVO extends PageCriteria<InletAnalysisDayDO> {

    /**
     * 集水面积ID
     * 
     */
    private Long catchmentId;

    /**
     * 水源ID
     * 
     */
    private Long swhsId;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 日
     * 
     */
    private String day;

    /**
     * 累计降雨量
     * 
     */
    private Double rainfall;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;

}

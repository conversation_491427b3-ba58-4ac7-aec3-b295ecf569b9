package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetail.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;

/**
 *
 * 来水分析明细 Base VO
 * @description 来水分析明细 Base VO，提供给添加、修改、详细的子 VO 使用
 * <AUTHOR>
 * @time 2024-07-23 14:33:37
 *
 */
@Data
public class InletAnalysisDetailBaseVO {

    /**
     * 集水面积ID
     * 
     */
    @NotNull(message = "集水面积ID不能为空")
    private Long catchmentId;
    /**
     * 集水面积名称
     */
    private String catchmentName;

    /**
     * 水源ID
     */
    private Long swhsId;
    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 水渠ID
     */
    private Long chanId;
    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 降雨量
     * 
     */
    private Double rainfall;

    /**
     * 蒸发量
     * 
     */
    private Double evaporation;

    /**
     * 渗透值
     * 
     */
    private Double penetration;

    /**
     * 径流值
     * 
     */
    private Double runoff;

    /**
     * 径流时间
     * @mock （分钟）
     */
    private Integer runoffTime;
}

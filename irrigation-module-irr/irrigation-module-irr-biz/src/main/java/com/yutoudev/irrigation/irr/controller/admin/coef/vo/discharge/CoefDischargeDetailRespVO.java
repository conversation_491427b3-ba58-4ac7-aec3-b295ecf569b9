package com.yutoudev.irrigation.irr.controller.admin.coef.vo.discharge;

import lombok.Data;
import lombok.ToString;

/**
 * 流量系数DetailResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-流量系数DetailResponseVO
 * @time 2024-08-05 23:32:43
 */
@Data
@ToString(callSuper = true)
public class CoefDischargeDetailRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 涵闸类型
     */
    private Integer gateType;

    /**
     * 涵闸分组
     */
    private Integer gateGroup;

    /**
     * 水流形态
     */
    private Integer flowPattern;

    /**
     * 墙面类型
     */
    private Integer wallType;

    /**
     * 系数
     */
    private Double coef;
}

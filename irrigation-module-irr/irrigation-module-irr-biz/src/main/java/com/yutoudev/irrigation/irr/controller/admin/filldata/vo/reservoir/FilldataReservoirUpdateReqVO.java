package com.yutoudev.irrigation.irr.controller.admin.filldata.vo.reservoir;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * 数据上报-水源可用水量更新RequestVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FilldataReservoirUpdateReqVO extends FilldataReservoirBaseVO {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}
package com.yutoudev.irrigation.irr.service.measanalysisday;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.irr.controller.admin.catchment.vo.catchmentarea.CatchmentAreaQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.controller.admin.measanalysisday.vo.*;
import com.yutoudev.irrigation.irr.convert.measanalysisday.MeasAnalysisDayConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.catchment.CatchmentAreaDO;
import com.yutoudev.irrigation.irr.dal.dataobject.catchment.CatchmentAreaEquipDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measanalysisday.MeasAnalysisDayDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndDayDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.dal.mysql.measanalysisday.MeasAnalysisDayMapper;
import com.yutoudev.irrigation.irr.service.catchment.CatchmentAreaEquipService;
import com.yutoudev.irrigation.irr.service.catchment.CatchmentAreaService;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndDayService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndHourService;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;
import static com.yutoudev.irrigation.irr.enums.ErrorCodeConstants.*;
import static com.yutoudev.irrigation.irr.mq.MqConfigConstants.MEAS_DAY_WATERFALL_HOUR_DIFFERENCE;

/**
 *
 * 量测集水区设备雨量统计日表Service实现类
 * @description 管理后台-量测集水区设备雨量统计日表Service实现类
 * <AUTHOR>
 * @time 2024-08-11 16:07:22
 *
 */
@Service
@Validated
public class MeasAnalysisDayServiceImpl extends TalonsServiceImpl<MeasAnalysisDayMapper,MeasAnalysisDayDO> implements MeasAnalysisDayService<MeasAnalysisDayDO> {

    @Resource
    private MeasAnalysisDayMapper measAnalysisDayMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;

    @Resource
    private CatchmentAreaService<CatchmentAreaDO> catchmentAreaService;

    @Resource
    private CatchmentAreaEquipService<CatchmentAreaEquipDO> catchmentAreaEquipService;

    @Resource
    private MeasIndDayService<MeasIndDayDO> measIndDayService;

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndHourService;

    @Override
    public void checkField(MeasAnalysisDayDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(MeasAnalysisDayCreateReqVO createReqVO) {
        // 插入
        MeasAnalysisDayDO measAnalysisDay = MeasAnalysisDayConvert.INSTANCE.convert(createReqVO);
        this.save(measAnalysisDay, true);
        // 返回
        return measAnalysisDay.getId();
    }

    @Override
    public boolean createBatch(List<MeasAnalysisDayCreateReqVO> list) {

        List<MeasAnalysisDayDO> saveList = MeasAnalysisDayConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        }else{
            throw exception(MEAS_ANALYSIS_DAY_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public boolean saveBatch(List<MeasAnalysisDayDO> list) {
        return measAnalysisDayMapper.insertOrUpdateBatch(list);
    }

    @Override
    public void update(MeasAnalysisDayUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateMeasAnalysisDayExists(updateReqVO.getId());
        // 更新
        MeasAnalysisDayDO updateObj = MeasAnalysisDayConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    public boolean updateBatch(List<MeasAnalysisDayUpdateReqVO> list) {

        List<MeasAnalysisDayDO> updateList = MeasAnalysisDayConvert.INSTANCE.convertUpdateBatch(list);

        for(MeasAnalysisDayDO tempDO: updateList){
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateMeasAnalysisDayExists(tempDO.getId());
        }

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(MEAS_ANALYSIS_DAY_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateMeasAnalysisDayExists(id);
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(MEAS_ANALYSIS_DAY_DELETE_BATCH_ERROR);
        }
    }

    private void validateMeasAnalysisDayExists(Long id) {
        if (measAnalysisDayMapper.selectById(id) == null) {
            throw exception(MEAS_ANALYSIS_DAY_NOT_EXISTS);
        }
    }

    @Override
    public MeasAnalysisDayDO get(Long id) {
        return this.getById(id, true);
    }

    @Override
    public MeasAnalysisDayDO getCatchmentDayData(Long catchmentId, String day) {
        return this.getOne(new LambdaQueryWrapperX<MeasAnalysisDayDO>()
                .eq(MeasAnalysisDayDO::getCatchmentId, catchmentId)
                .eq(MeasAnalysisDayDO::getDay, day)
        );
    }

    @Override
    public List<MeasAnalysisDayDO> getListByCatchmentAndMonth(Long catchmentId, String month) {
        return this.list(new QueryWrapper<MeasAnalysisDayDO>()
                .eq("catchment_id", catchmentId)
                .apply("date_format(day,'%Y-%m') = {0}", month)
        );
    }

    @Override
    public List<MeasAnalysisDayDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public PageResult<MeasAnalysisDayDO> page(MeasAnalysisDayPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<MeasAnalysisDayDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasAnalysisDayDO>() { };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<MeasAnalysisDayDO> pageResult = measAnalysisDayMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
            talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<MeasAnalysisDayDO> getList(MeasAnalysisDayQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<MeasAnalysisDayDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasAnalysisDayDO>() { };
        queryBuilder.build(queryReqVO);

        List<MeasAnalysisDayDO> result = measAnalysisDayMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<MeasAnalysisDayExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(MEAS_ANALYSIS_DAY_IMPORT_LIST_IS_EMPTY);
        }

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<MeasAnalysisDayDO> saveList = MeasAnalysisDayConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            MeasAnalysisDayDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, MeasAnalysisDayExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }

    @Override
    public String calCatchmentRainfallAvg(Integer intervalTime) {

        // 获取集水面积列表
        List<CatchmentAreaDO> catchmentAreaList = catchmentAreaService.getList(new CatchmentAreaQueryReqVO());

        if (CollectionUtils.isEmpty(catchmentAreaList)) {
            return "未设置集水面积";
        }

        for (CatchmentAreaDO catchmentArea : catchmentAreaList) {
            List<CatchmentAreaEquipDO> areaEquips = catchmentAreaEquipService.getListByAreaId(catchmentArea.getId());
            if (!CollectionUtils.isEmpty(areaEquips)) {

                Map<Long, CatchmentAreaEquipDO> map = new HashMap<>(5);
                for (CatchmentAreaEquipDO ce : areaEquips) {
                    EquipBaseCacheVO cache = equipBaseService.getCacheByCentralIdAndDevId(ce.getCentralId(), ce.getDevId());
                    if (Objects.isNull(cache)) {
                        continue;
                    }
                    map.put(cache.getId(), ce);
                }

                if (map.isEmpty()) {
                    continue;
                }

                // 计算当前时间所属日期
                LocalDateTime now = LocalDateTime.now().minusHours(MEAS_DAY_WATERFALL_HOUR_DIFFERENCE).minusMinutes(intervalTime + 1);
                String day = now.format(DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY));

                // 查询日统计表中各设备的数据
                List<MeasIndDayDO> dayData = measIndDayService.getListByEquipIdsAndDay(map.keySet().stream().toList(), day);

                if (CollectionUtils.isEmpty(dayData)) {
                    continue;
                }

                Set<Long> collect = dayData.stream().map(MeasIndDayDO::getEquipId).collect(Collectors.toSet());
                double areaSum = areaEquips.stream().filter(item -> collect.contains(item.getEquipId())).mapToDouble(CatchmentAreaEquipDO::getArea).sum();

                // 查询计算日平均降水是否存在数据
                MeasAnalysisDayDO catchmentDayData = getCatchmentDayData(catchmentArea.getId(), day);

                // 平均降水量根据日表计算
                Float avgChild = dayData.stream().map(MeasIndDayDO::getRainfallVolume).reduce(0f, Float::sum);
                // avgChild除以map的长度之后保留2位小数
                float avg = new BigDecimal(avgChild).divide(new BigDecimal(map.size()), 2, RoundingMode.HALF_UP).floatValue();

                // 加权平均值分子根据日表计算
                double weightedAvgChild = 0.0;
                // 对map进行遍历
                for (Map.Entry<Long, CatchmentAreaEquipDO> entry : map.entrySet()) {
                    Long equipId = entry.getKey();
                    CatchmentAreaEquipDO equip = entry.getValue();
                    // 从dayData中过滤出equipId对应的数据
                    Optional<MeasIndDayDO> data = dayData.stream().filter(d -> d.getEquipId().equals(equipId)).findFirst();
                    if (data.isPresent()) {
                        // 重新计算权重
                        Double ratio = BigDecimal.valueOf(equip.getArea()).divide(BigDecimal.valueOf(areaSum), 3, RoundingMode.HALF_UP).doubleValue();
                        weightedAvgChild += data.get().getRainfallVolume() * ratio;
                    }
                }

                // 加权平均值，分母为1，故不再相除
                float weightedAvg = convert(weightedAvgChild, 1).floatValue();

                if (Objects.isNull(catchmentDayData)) {
                    MeasAnalysisDayCreateReqVO create = new MeasAnalysisDayCreateReqVO();
                    create.setCatchmentId(catchmentArea.getId());
                    create.setDay(day);
                    create.setEquipCount(dayData.size());
                    create.setAvgRainfall(avg);
                    create.setWeightedAvgRainfall(weightedAvg);
                    create.setUpdateTime(LocalDateTime.now());
                    create(create);
                } else {
                    MeasAnalysisDayUpdateReqVO update = new MeasAnalysisDayUpdateReqVO();
                    update.setId(catchmentDayData.getId());
                    update.setEquipCount(dayData.size());
                    update.setAvgRainfall(avg);
                    update.setWeightedAvgRainfall(weightedAvg);
                    update.setUpdateTime(LocalDateTime.now());
                    update(update);
                }
            }
        }

        return "计算集水区域设备雨量平均值任务完成";
    }
}

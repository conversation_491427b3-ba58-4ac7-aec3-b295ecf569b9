package com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 流域降雨过程明细DO
 *
 * <AUTHOR>
 */
@TableName(value = "irr_rainfall_process_basin_item", autoResultMap = true)
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@KeySequence("irr_rainfall_process_basin_item_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RainfallProcessBasinItemDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 流域降雨过程ID
     */
    private Long processId;

    /**
     * 时间
     */
    private LocalDateTime reportTime;

    /**
     * 降雨量
     */
    private Double rainfall;

    /**
     * 蒸发量
     */
    private Double evaporation;

    /**
     * 蒸散发量
     */
    private Double evapotranspiration;

    /**
     * 库容量
     */
    private Double capacity;

    /**
     * 时段库容差
     */
    private Double capacityDiff;

    /**
     * 出库水量
     */
    private Double yield;

    /**
     * 入库水量
     */
    private Double inflow;

    /**
     * 入库水量差
     */
    private Double inflowDiff;

    /**
     * 水面蒸发量
     */
    private Double surfaceEvaporation;

    /**
     * 入库径流深
     */
    private Double runoffDepth;

    /**
     * 径流系数
     */
    private Double runoffCoef;

    /**
     * 水源ID
     */
    private Long swhsId;


    @TableField(exist = false)
    private LocalDateTime createTime;

    @TableField(exist = false)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private String creator;

    @TableField(exist = false)
    private String updater;

    @TableField(exist = false)
    private Boolean deleted;
}

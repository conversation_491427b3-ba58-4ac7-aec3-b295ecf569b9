package com.yutoudev.irrigation.irr.convert.outletbase;

import com.yutoudev.irrigation.framework.common.mapstruct.TypeConversion;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanOutletReqVO;
import com.yutoudev.irrigation.irr.controller.admin.outletbase.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.chanbase.ChanBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.outletbase.OutletBaseDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 * 出水口Convert
 * @description 管理后台-出水口 mapstruct Convert
 * <AUTHOR>
 * @time 2024-05-11 19:30:10
 *
 */
@Mapper(uses = TypeConversion.class)
public interface OutletBaseConvert {

    OutletBaseConvert INSTANCE = Mappers.getMapper(OutletBaseConvert.class);

    /**
    * CreateReqVO转为DO
    * @param createReqVO createReqVO
    * @return DO
    */
    OutletBaseDO convert(OutletBaseCreateReqVO createReqVO);

    /**
    * 批量把 CreateReqVO 转为 DO
    * @param list CreateReqVO list
    * @return DO list
    */
    List<OutletBaseDO> convertCreateBatch(List<OutletBaseCreateReqVO> list);

    /**
    * UpdateReqVO转为DO
    * @param updateReqVO updateReqVO
    * @return DO
    */
    OutletBaseDO convert(OutletBaseUpdateReqVO updateReqVO);

    /**
    * 批量把 UpdateReqVO转为DO
    * @param list UpdateReqVO list
    * @return DO
    */
    List<OutletBaseDO> convertUpdateBatch(List<OutletBaseUpdateReqVO> list);

    /**
    * DO转化为RespVO
    * @param bean DO
    * @return RespVO
    */
    @Mappings({
            @Mapping(source = "createTime", target = "createTime", qualifiedByName = "convertLocalDateTimeToDate"),
            @Mapping(source = "updateTime", target = "updateTime", qualifiedByName = "convertLocalDateTimeToDate")
    })
    OutletBaseRespVO convert(OutletBaseDO bean);

    /**
    * DO转化为 DetailRespVO
    * @param bean DO
    * @return DetailRespVO
    */
    @Mappings({
            @Mapping(source = "createTime", target = "createTime", qualifiedByName = "convertLocalDateTimeToDate"),
    })
    OutletBaseDetailRespVO convertDetail(OutletBaseDO bean);

    /**
    * 批量把DO转化为RespVO
    * @param list DO list
    * @return RespVO list
    */
    List<OutletBaseRespVO> convertList(List<OutletBaseDO> list);

    /**
    * 把PageResult中的DO转化为RespVO
    * @param page PageResult DO
    * @return PageResult RespVO
    */
    PageResult<OutletBaseRespVO> convertPage(PageResult<OutletBaseDO> page);


    @Mappings({
            @Mapping(source = "createTime", target = "createTime", qualifiedByName = "convertLocalDateTimeToDate"),
    })
    OutletBaseExcelVO convertExportExcel(OutletBaseDO bean);
    /**
    * 批量把DO转化为ExcelVO
    * @param list DO list
    * @return ExcelVO list
    */
    List<OutletBaseExcelVO> convertExportExcel(List<OutletBaseDO> list);

    /**
    * 批量把ExcelVO转化为DO
    * @param list ExcelVO list
    * @return DO list
    */
    List<OutletBaseDO> convertImportExcel(List<OutletBaseExcelVO> list);

    /**
     * 把水渠出水口转化为出水口
     * @param outlet
     * @return
     */
    OutletBaseDO convert(ChanOutletReqVO outlet);


    @Mappings({
            @Mapping(source = "createTime", target = "createTime", qualifiedByName = "convertLocalDateTimeToDate"),
            @Mapping(source = "updateTime", target = "updateTime", qualifiedByName = "convertLocalDateTimeToDate")
    })
    ChanBaseRespVO convertChanBaseResp(ChanBaseDO bean);

}

package com.yutoudev.irrigation.irr.controller.admin.division;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.division.vo.*;
import com.yutoudev.irrigation.irr.convert.division.DivisionTownConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.division.DivisionTownDO;
import com.yutoudev.irrigation.irr.service.division.DivisionTownService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 行政区划-乡镇
 *
 * <AUTHOR>
 * @description 管理后台-行政区划-乡镇controller
 * @time 2024-11-30 10:09:04
 */
@RestController
@RequestMapping("/irr/division-town")
@Validated
public class DivisionTownController {

    private static final String MODULE_NAME = "行政区划-乡镇";

    @Resource
    private DivisionTownService<DivisionTownDO> divisionTownService;

    /**
     * 创建行政区划-乡镇
     *
     * @param createReqVO DivisionTownCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:division-town:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody DivisionTownCreateReqVO createReqVO) {
        return success(divisionTownService.create(createReqVO));
    }

    /**
     * 批量创建行政区划-乡镇
     *
     * @param lists DivisionTownCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:division-town:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<DivisionTownCreateReqVO> lists) {
        return success(divisionTownService.createBatch(lists));
    }

    /**
     * 更新行政区划-乡镇
     *
     * @param updateReqVO DivisionTownUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:division-town:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody DivisionTownUpdateReqVO updateReqVO) {
        divisionTownService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新行政区划-乡镇
     *
     * @param lists 批量更新列表 DivisionTownUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:division-town:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<DivisionTownUpdateReqVO> lists) {
        return success(divisionTownService.updateBatch(lists));
    }

    /**
     * 删除行政区划-乡镇
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:division-town:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        divisionTownService.delete(id);
        return success(true);
    }

    /**
     * 批量删除行政区划-乡镇
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:division-town:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(divisionTownService.deleteBatch(ids));
    }

    /**
     * 获得行政区划-乡镇详情
     *
     * @param id 编号 Long
     * @return CommonResult<DivisionTownDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:division-town:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<DivisionTownDetailRespVO> get(@RequestParam("id") Long id) {
        DivisionTownDO divisionTown = divisionTownService.get(id);
        return success(DivisionTownConvert.INSTANCE.convertDetail(divisionTown));
    }

    /**
     * 行政区划-乡镇列表
     *
     * @param queryReqVO 查询条件 DivisionTownQueryReqVO
     * @return CommonResult<List < DivisionTownRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:division-town:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<DivisionTownRespVO>> getList(@RequestQueryParam DivisionTownQueryReqVO queryReqVO) {
        List<DivisionTownDO> list = divisionTownService.getList(queryReqVO);
        return success(DivisionTownConvert.INSTANCE.convertList(list));
    }

    /**
     * 行政区划-乡镇列表GIS
     *
     * @param queryReqVO 查询条件 DivisionTownQueryReqVO
     * @return CommonResult<List < DivisionTownGisRespVO>> 列表响应VO
     */
    @GetMapping("/listGeo")
    @PreAuthorize("@ss.hasPermission('irr:division-town:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<DivisionTownGisRespVO>> getListGeo(@RequestQueryParam DivisionTownQueryReqVO queryReqVO) {
        List<DivisionTownGisRespVO> list = divisionTownService.getListByGis(queryReqVO);
        return success(list);
    }

    /**
     * 行政区划-乡镇分页
     *
     * @param pageVO 查询条件 DivisionTownPageReqVO
     * @return CommonResult<PageResult < DivisionTownRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:division-town:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<DivisionTownRespVO>> page(@RequestQueryParam DivisionTownPageReqVO pageVO) {
        PageResult<DivisionTownDO> pageResult = divisionTownService.page(pageVO);
        return success(DivisionTownConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出行政区划-乡镇Excel
     *
     * @param queryReqVO 查询条件 DivisionTownExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:division-town:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam DivisionTownExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<DivisionTownDO> list = divisionTownService.getList(queryReqVO);
        // 导出 Excel
        List<DivisionTownExcelVO> datas = DivisionTownConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "行政区划-乡镇", "xlsx"), queryReqVO.getExportSheetName(),
                DivisionTownExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入行政区划-乡镇模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:division-town:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "行政区划-乡镇-导入模版.xls", "sheet1", DivisionTownExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入行政区划-乡镇Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:division-town:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<DivisionTownExcelVO> list = ExcelUtils.read(file, DivisionTownExcelVO.class);
        return success(divisionTownService.importExcel(list, isUpdate));
    }
}
package com.yutoudev.irrigation.irr.service.history;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.irr.controller.admin.history.vo.evaporationday.*;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryEvaporationDayDO;
import io.github.portaldalaran.talons.core.ITalonsService;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 历史日均蒸散发量Service接口
 *
 * <AUTHOR>
 * @description 管理后台-历史日均蒸散发量Service接口，对ITalonsService的扩展
 * @time 2024-12-20 19:58:36
 */
public interface HistoryEvaporationDayService<T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建历史日均蒸散发量
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid HistoryEvaporationDayCreateReqVO createReqVO);

    /**
     * 批量创建历史日均蒸散发量
     *
     * @param list 创建信息
     * @return 编号
     */
    boolean createBatch(@Valid List<HistoryEvaporationDayCreateReqVO> list);

    /**
     * 更新历史日均蒸散发量
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid HistoryEvaporationDayUpdateReqVO updateReqVO);

    /**
     * 批量更新历史日均蒸散发量
     *
     * @param list 更新信息
     */
    boolean updateBatch(@Valid List<HistoryEvaporationDayUpdateReqVO> list);

    /**
     * 删除历史日均蒸散发量
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
     * 批量删除历史日均蒸散发量
     *
     * @param ids 编号
     */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得历史日均蒸散发量
     *
     * @param id 编号
     * @return 历史日均蒸散发量
     */
    HistoryEvaporationDayDO get(Long id);

    /**
     * 历史日均蒸散发量列表
     *
     * @param ids 编号
     * @return 历史日均蒸散发量列表
     */
    List<HistoryEvaporationDayDO> getList(List<Long> ids);

    /**
     * 历史日均蒸散发量分页
     *
     * @param pageReqVO 分页查询
     * @return 历史日均蒸散发量分页
     */
    PageResult<HistoryEvaporationDayDO> page(HistoryEvaporationDayPageReqVO pageReqVO);

    /**
     * 获得历史日均蒸散发量列表,
     *
     * @param queryReqVO 查询条件键值对
     * @return 历史日均蒸散发量列表
     */
    List<HistoryEvaporationDayDO> getList(HistoryEvaporationDayQueryReqVO queryReqVO);

    /**
     * 批量导入历史日均蒸散发量 excel
     *
     * @param importList      导入历史日均蒸散发量列表
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    ImportExcelRespVO importExcel(List<HistoryEvaporationDayExcelVO> importList, boolean isUpdateSupport);
}
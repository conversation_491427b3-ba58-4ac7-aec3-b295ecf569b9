package com.yutoudev.irrigation.irr.service.rainfallprocess.dto;

import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourRainfallSimpleRespVO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RainfallProcessMergedDTO {

    /**
     * 时段序号
     */
    private Integer period;

    /**
     * 时段名称
     */
    private String periodName;

    /**
     * 开始降雨时间
     */
    private LocalDateTime startTime;

    /**
     * 结束降雨时间
     */
    private LocalDateTime endTime;

    /**
     * 降雨时长
     */
    private Long duration;

    /**
     * 降雨数据
     */
    private List<MeasIndHourRainfallSimpleRespVO> rainfallDataList;

    /**
     * 降雨量
     */
    private Float rainfallVolume;
}

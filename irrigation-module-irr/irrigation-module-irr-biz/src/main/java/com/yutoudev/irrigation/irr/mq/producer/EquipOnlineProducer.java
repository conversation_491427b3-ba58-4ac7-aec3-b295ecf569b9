package com.yutoudev.irrigation.irr.mq.producer;

import com.yutoudev.irrigation.irr.mq.message.EquipOnlineSendQueueMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.yutoudev.irrigation.irr.mq.MqNamingConstants.IOT_EQUIP_EXCHANGE;
import static com.yutoudev.irrigation.irr.mq.MqNamingConstants.IOT_ONLINE_SEND_ROUTING_KEY;

/**
 * 发起查询设备在线状态指令
 * <AUTHOR>
 */
@Component
@Slf4j
public class EquipOnlineProducer {

    @Autowired(required = false)
    private RabbitTemplate rabbitTemplate;

    /**
     * 发起查询设备在线状态指令
     * @param msg 发起查询设备在线状态指令
     */
    public void send(EquipOnlineSendQueueMessage msg) {
        rabbitTemplate.convertAndSend(IOT_EQUIP_EXCHANGE, IOT_ONLINE_SEND_ROUTING_KEY, msg);
    }

}

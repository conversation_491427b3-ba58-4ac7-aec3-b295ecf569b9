package com.yutoudev.irrigation.irr.dal.dataobject.insibase;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.chanbase.ChanBaseDO;
import com.yutoudev.irrigation.irr.dal.mysql.chanbase.ChanBaseMapper;
import io.github.portaldalaran.talons.annotation.ManyToOne;
import lombok.*;

import java.util.Date;

/**
 *
 * 倒虹吸DO
 * @description 管理后台-倒虹吸数据库对象
 * <AUTHOR>
 * @time 2024-05-11 13:50:19
 *
 */
@TableName(value = "irr_insi_base", autoResultMap = true)
@KeySequence("irr_insi_base_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsiBaseDO extends BaseDO {

    /**
     * ID
     * 
     */
    @TableId
    private Long id;

    /**
     * 倒虹吸代码
     * 
     */
    private String insiCode;

    /**
     * 倒虹吸名称
     * 
     */
    private String insiName;

    /**
     * 起点经度
     * 
     */
    private Double startLng;

    /**
     * 起点纬度
     * 
     */
    private Double startLat;

    /**
     * 终点经度
     * 
     */
    private Double endLng;

    /**
     * 终点纬度
     * 
     */
    private Double endLat;

    /**
     * 倒虹吸GIS
     * 
     */
    private String insiLocGis;

    /**
     * 倒虹吸所在位置
     * 
     */
    private String insiLoc;

    /**
     * 倒虹吸类型
     * 
     *
     * 枚举 {@link //TODO irr_insi_type 对应的类}
     */
    private Integer insiType;

    /**
     * 管道净高
     * 
     */
    private Double pipeNetHeig;

    /**
     * 管道净宽
     * 
     */
    private Double pipeNetWid;

    /**
     * 管道内径
     * 
     */
    private Double pipeIndi;

    /**
     * 孔数
     * 
     */
    private Integer orifNum;

    /**
     * 倒虹吸过水能力
     * 
     */
    private Double insiWatProp;

    /**
     * 基础结构型式
     * 
     */
    private String baseStrPatt;

    /**
     * 工程建设情况
     * 
     *
     * 枚举 {@link //TODO irr_eng_stat 对应的类}
     */
    private Integer engStat;

    /**
     * 开工时间
     * 
     */
    private Date startDate;

    /**
     * 建成时间
     * 
     */
    private Date compDate;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 运行状态
     * @mock 1:正常、2异常、3废弃
     */
    private Integer runStatus;

    /**
     * @apiNote 多对一关联, 用于查询时
     */
    @TableField(exist = false)
    @ManyToOne(targetEntity = ChanBaseDO.class, targetMapper = ChanBaseMapper.class)
    private ChanBaseDO chan;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水渠代码
     * 
     */
    private String chanCode;

    /**
     * 水渠名称
     * 
     */
    private String chanName;

    /**
     * 桩号
     */
    private String mlgNum;

}

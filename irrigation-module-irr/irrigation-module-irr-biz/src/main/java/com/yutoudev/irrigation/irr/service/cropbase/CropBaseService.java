package com.yutoudev.irrigation.irr.service.cropbase;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.irr.controller.admin.cropbase.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.cropbase.CropBaseDO;
import io.github.portaldalaran.talons.core.ITalonsService;

import jakarta.validation.Valid;
import java.util.List;

/**
 *
 * 作物配额Service接口
 * @description 管理后台-作物配额Service接口，对ITalonsService的扩展
 * <AUTHOR>
 * @time 2024-05-13 10:29:50
 *
 */
public interface CropBaseService <T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建作物配额
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid CropBaseCreateReqVO createReqVO);

    /**
    * 批量创建作物配额
    *
    * @param list 创建信息
    * @return 编号
    */
    boolean createBatch(@Valid List<CropBaseCreateReqVO> list);

    /**
     * 更新作物配额
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid CropBaseUpdateReqVO updateReqVO);

    /**
    * 批量更新作物配额
    *
    * @param list 更新信息
    */
    boolean updateBatch(@Valid List<CropBaseUpdateReqVO> list);

    /**
     * 删除作物配额
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
    * 批量删除作物配额
    *
    * @param ids 编号
    */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得作物配额
     *
     * @param id 编号
     * @return 作物配额
     */
    CropBaseDO get(Long id);
    /**
     * 作物配额列表
     *
     * @param ids 编号
     * @return 作物配额列表
     */
    List<CropBaseDO> getList(List<Long> ids);

    /**
     * 作物配额分页
     *
     * @param pageReqVO 分页查询
     * @return 作物配额分页
     */
    PageResult<CropBaseDO> page(CropBasePageReqVO pageReqVO);

    /**
    * 获得作物配额列表,
    *
    * @param queryReqVO 查询条件键值对
    * @return 作物配额列表
    */
    List<CropBaseDO> getList(CropBaseQueryReqVO queryReqVO);

    /**
    * 批量导入作物配额 excel
    *
    * @param importList     导入作物配额列表
    * @param isUpdateSupport 是否支持更新
    * @return 导入结果
    */
    ImportExcelRespVO importExcel(List<CropBaseExcelVO> importList, boolean isUpdateSupport);
}
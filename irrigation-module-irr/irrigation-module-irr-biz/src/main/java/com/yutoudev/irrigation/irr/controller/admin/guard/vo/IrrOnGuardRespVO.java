package com.yutoudev.irrigation.irr.controller.admin.guard.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IrrOnGuardRespVO extends IrrOnGuardBaseVO {
    private Long id;
    private Date createTime;

    private String guardLeaderName;

    private String guardPersonName;
} 
package com.yutoudev.irrigation.irr.controller.admin.waterfee.vo.waterpay;

import com.yutoudev.irrigation.irr.dal.dataobject.waterfee.WaterPayDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 水费收费单list查询RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-水费收费单list查询RequestVO，参数和 WaterPayPageReqVO 是一致的
 * @time 2024-08-25 23:07:19
 */
@Data
public class WaterPayQueryReqVO extends QueryCriteria<WaterPayDO> {

    /**
     * ID
     */
    private Long id;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 水费账单id
     *
     * @mock 关联irr_water_bill.id
     */
    private Long waterBillId;

    /**
     * 缴费时间
     */
    private LocalDateTime payTime;

    /**
     * 缴费方式
     *
     * @mock 0微信 1现金 9其它
     */
    private Integer payType;

    /**
     * 收银人员
     */
    private String cashier;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用水单位id
     */
    private Long deptId;

    /**
     * 用户单位名称
     */
    private String deptName;

    /**
     * 用水户id
     */
    private Long userId;

    /**
     * 用水户名称
     */
    private String userName;

    /**
     * 水价标准
     */
    private Long standardPriceId;

    /**
     * 水价标准名称
     */
    private String standardPriceName;

    /**
     * 账单月份
     */
    private LocalDate billMonth;

    /**
     * 用水总量
     *
     * @mock 吨
     */
    private Double waterTotal;

    /**
     * 实缴金额
     *
     * @mock 元
     */
    private Double payAmount;
}

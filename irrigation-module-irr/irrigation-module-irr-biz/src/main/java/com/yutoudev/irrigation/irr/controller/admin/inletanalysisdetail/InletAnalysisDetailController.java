package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetail;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.validation.constraints.*;
import javax.validation.*;
import java.util.*;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;


import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetail.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisdetail.InletAnalysisDetailDO;
import com.yutoudev.irrigation.irr.convert.inletanalysisdetail.InletAnalysisDetailConvert;
import com.yutoudev.irrigation.irr.service.inletanalysisdetail.InletAnalysisDetailService;


/**
 *
 * 来水分析明细
 * @description 管理后台-来水分析明细controller
 * <AUTHOR>
 * @time 2024-07-23 14:33:37
 *
 */
@RestController
@RequestMapping("/irr/inlet-analysis-detail")
@Validated
public class InletAnalysisDetailController {

    private static final String MODULE_NAME = "来水分析明细";

    @Resource
    private InletAnalysisDetailService<InletAnalysisDetailDO> inletAnalysisDetailService;

    /**
     * 创建来水分析明细
     * @description 单个对象保存
     * @param createReqVO InletAnalysisDetailCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody InletAnalysisDetailCreateReqVO createReqVO) {
        return success(inletAnalysisDetailService.create(createReqVO));
    }

    /**
     * 批量创建来水分析明细
     * @description 多个对象保存
     * @param lists  InletAnalysisDetailCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<InletAnalysisDetailCreateReqVO> lists) {
        return success(inletAnalysisDetailService.createBatch(lists));
    }

    /**
     * 更新来水分析明细
     * @description 单个对象修改
     * @param updateReqVO InletAnalysisDetailUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody InletAnalysisDetailUpdateReqVO updateReqVO) {
        inletAnalysisDetailService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新来水分析明细
     * @description 批量更新
     * @param lists 批量更新列表 InletAnalysisDetailUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<InletAnalysisDetailUpdateReqVO> lists) {
        return success(inletAnalysisDetailService.updateBatch(lists));
    }

    /**
     * 删除来水分析明细
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        inletAnalysisDetailService.delete(id);
        return success(true);
    }

    /**
     * 批量删除来水分析明细
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(inletAnalysisDetailService.deleteBatch(ids));
    }

    /**
     * 获得来水分析明细详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<InletAnalysisDetailDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<InletAnalysisDetailDetailRespVO> get(@RequestParam("id") Long id) {
        InletAnalysisDetailDO inletAnalysisDetail = inletAnalysisDetailService.get(id);
        return success(InletAnalysisDetailConvert.INSTANCE.convertDetail(inletAnalysisDetail));
    }

    /**
     * 来水分析明细列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 InletAnalysisDetailQueryReqVO
     * @return CommonResult<List<InletAnalysisDetailRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<InletAnalysisDetailRespVO>> getList(@RequestQueryParam InletAnalysisDetailQueryReqVO queryReqVO) {
        List<InletAnalysisDetailDO> list = inletAnalysisDetailService.getList(queryReqVO);
        return success(InletAnalysisDetailConvert.INSTANCE.convertList(list));
    }

    /**
     * 来水分析明细分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 InletAnalysisDetailPageReqVO
     * @return CommonResult<PageResult<InletAnalysisDetailRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<InletAnalysisDetailRespVO>> page(@RequestQueryParam InletAnalysisDetailPageReqVO pageVO) {
        PageResult<InletAnalysisDetailDO> pageResult = inletAnalysisDetailService.page(pageVO);
        return success(InletAnalysisDetailConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出来水分析明细Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 InletAnalysisDetailExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam InletAnalysisDetailExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<InletAnalysisDetailDO> list = inletAnalysisDetailService.getList(queryReqVO);
        // 导出 Excel
        List<InletAnalysisDetailExcelVO> datas = InletAnalysisDetailConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "来水分析明细", "xlsx"), queryReqVO.getExportSheetName(),
                                                    InletAnalysisDetailExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入来水分析明细模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "来水分析明细-导入模版.xls", "sheet1", InletAnalysisDetailExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入来水分析明细Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:inlet-analysis-detail:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<InletAnalysisDetailExcelVO> list = ExcelUtils.read(file, InletAnalysisDetailExcelVO.class);
        return success(inletAnalysisDetailService.importExcel(list, isUpdate));
    }
}
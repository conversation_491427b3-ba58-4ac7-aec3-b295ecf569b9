package com.yutoudev.irrigation.irr.controller.admin.usewaterapply.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;
import java.util.List;


/**
 * 用水申请审核RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-用水申请审核RequestVO
 */
@Data
@ToString(callSuper = true)
public class UsewaterApplyExamineReqVO {
    private Long id;
    private List<Long> ids;
}
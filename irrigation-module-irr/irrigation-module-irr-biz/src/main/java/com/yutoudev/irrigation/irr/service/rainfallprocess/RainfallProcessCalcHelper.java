package com.yutoudev.irrigation.irr.service.rainfallprocess;

import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourRainfallSimpleRespVO;
import com.yutoudev.irrigation.irr.dal.dataobject.catchment.CatchmentAreaEquipDO;
import com.yutoudev.irrigation.irr.enums.RainfallProcessCalcTypeEnum;
import com.yutoudev.irrigation.irr.service.rainfallprocess.dto.RainfallProcessMergedDTO;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;

/**
 * <AUTHOR>
 */
@Component
public class RainfallProcessCalcHelper {

    /**
     * 合并降雨数据
     *
     * @param originalHourRainfallList 小数降雨原始数据列表
     * @return 合并后的降雨数据
     */
    public List<RainfallProcessMergedDTO> merge(List<MeasIndHourRainfallSimpleRespVO> originalHourRainfallList,
                                                int calcType,
                                                List<CatchmentAreaEquipDO> areaEquipList
    ) {
        // 提取所有有降雨的时间点（所有设备的并集），并转换为实际降雨时间段
        List<RainfallPeriod> rainfallPeriods = originalHourRainfallList.stream()
                .filter(r -> r.getRainfallVolume() > 0)
                .map(r -> new RainfallPeriod(r.getHourTime().minusHours(1), r.getHourTime()))
                .distinct()
                .sorted(Comparator.comparing(RainfallPeriod::startTime))
                .toList();

        return mergeContinuousRainfallPeriods(originalHourRainfallList, rainfallPeriods, calcType, areaEquipList);

    }

    /**
     * 合并连续的降雨时间段
     *
     * @param originalHourRainfallList 原始小数降雨数据列表
     * @param sortedPeriods 已排序的降雨时间段列表
     * @param calcType 计算类型 1 流域 2 站点
     * @param areaEquipList 集水面积设备列表
     * @return 合并后的降雨时间段列表
     */
    private List<RainfallProcessMergedDTO> mergeContinuousRainfallPeriods(
            List<MeasIndHourRainfallSimpleRespVO> originalHourRainfallList,
            List<RainfallPeriod> sortedPeriods,
            int calcType,
            List<CatchmentAreaEquipDO> areaEquipList
    ) {
        List<RainfallProcessMergedDTO> mergedPeriods = new ArrayList<>();
        if (sortedPeriods.isEmpty()) {
            return List.of();
        }

        // 初始化第一个降雨时间段
        RainfallPeriod currentPeriod = sortedPeriods.get(0);
        LocalDateTime mergedStart = currentPeriod.startTime();
        LocalDateTime mergedEnd = currentPeriod.endTime();
        int periodCounter = 1;

        for (int i = 1; i < sortedPeriods.size(); i++) {
            RainfallPeriod nextPeriod = sortedPeriods.get(i);

            // 如果当前时间段的结束时间等于下一个时间段的开始时间，则合并
            if (mergedEnd.equals(nextPeriod.startTime())) {
                // 扩展结束时间
                mergedEnd = nextPeriod.endTime();
            } else {
                // 创建并添加合并的时间段
                RainfallProcessMergedDTO mergedPeriod = createMergedPeriod(
                        originalHourRainfallList, areaEquipList, calcType,
                        mergedStart, mergedEnd, periodCounter, currentPeriod.startTime().getYear());
                mergedPeriods.add(mergedPeriod);

                periodCounter++;

                // 开始新的时间段
                mergedStart = nextPeriod.startTime();
                mergedEnd = nextPeriod.endTime();
            }
        }

        // 添加最后一个时间段
        RainfallProcessMergedDTO lastMergedPeriod = createMergedPeriod(
                originalHourRainfallList, areaEquipList, calcType,
                mergedStart, mergedEnd, periodCounter, currentPeriod.startTime().getYear());
        mergedPeriods.add(lastMergedPeriod);

        return mergedPeriods;
    }

    /**
     * 创建合并的降雨时间段对象
     *
     * @param originalHourRainfallList    原始降雨数据列表
     * @param areaEquipList   集水面积设备列表
     * @param calcType        计算类型
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param periodCounter   时间段计数器
     * @param year            年份
     * @return 合并的降雨时间段对象
     */
    private RainfallProcessMergedDTO createMergedPeriod(List<MeasIndHourRainfallSimpleRespVO> originalHourRainfallList,
                                                        List<CatchmentAreaEquipDO> areaEquipList,
                                                        int calcType,
                                                        LocalDateTime startTime,
                                                        LocalDateTime endTime,
                                                        int periodCounter,
                                                        int year) {
        RainfallProcessMergedDTO mergedPeriod = new RainfallProcessMergedDTO();

        // 设置基本信息
        mergedPeriod.setPeriod(periodCounter);
        mergedPeriod.setPeriodName(MessageFormat.format("{0}年，第{1}次", String.valueOf(year), periodCounter));
        mergedPeriod.setStartTime(startTime);
        mergedPeriod.setEndTime(endTime);
        mergedPeriod.setDuration(ChronoUnit.HOURS.between(startTime, endTime));

        // 获取降雨数据列表
        LocalDateTime filterEndTime = endTime.plusHours(1);
        List<MeasIndHourRainfallSimpleRespVO> filterList = originalHourRainfallList.stream()
                .filter(r -> r.getHourTime().isAfter(startTime) && r.getHourTime().isBefore(filterEndTime))
                .toList();
        mergedPeriod.setRainfallDataList(filterList);

        // 计算降雨量
        float rainfallVolume = calculateRainfallVolume(areaEquipList, filterList, calcType);
        mergedPeriod.setRainfallVolume(rainfallVolume);

        return mergedPeriod;
    }

    /**
     * 根据计算类型计算降雨量
     *
     * @param areaEquipList 集水面积设备列表
     * @param filterList    过滤后的降雨数据列表
     * @param calcType      计算类型
     * @return 降雨量
     */
    private float calculateRainfallVolume(List<CatchmentAreaEquipDO> areaEquipList,
                                          List<MeasIndHourRainfallSimpleRespVO> filterList,
                                          int calcType) {
        if (filterList.isEmpty()) {
            return 0.0f;
        }

        if (calcType == RainfallProcessCalcTypeEnum.BASIN.getType()) {
            return calculateRainfallPeriodVolume(areaEquipList, filterList);
        } else {
            return calculateRainfallPeriodVolume(filterList.get(0).getEquipId(), filterList);
        }
    }

    public record RainfallPeriod(LocalDateTime startTime, LocalDateTime endTime) {
    }

    public float calculateRainfallPeriodVolume(List<CatchmentAreaEquipDO> areaEquipList,
                                               List<MeasIndHourRainfallSimpleRespVO> hourRainfallList) {
        if (areaEquipList == null || areaEquipList.isEmpty() || hourRainfallList == null || hourRainfallList.isEmpty()) {
            return 0.0f;
        }

        // 构建设备ID到集水面积设备的映射
        Map<Long, CatchmentAreaEquipDO> equipMap = areaEquipList.stream()
                .collect(Collectors.toMap(CatchmentAreaEquipDO::getEquipId, Function.identity()));

        // 按小时时间分组降雨数据（hourRainfallList已经是过滤完成的数据）
        Map<LocalDateTime, List<MeasIndHourRainfallSimpleRespVO>> hourlyDataMap = hourRainfallList.stream()
                .collect(Collectors.groupingBy(MeasIndHourRainfallSimpleRespVO::getHourTime));

        double totalWeightedRainfall = 0.0;
        int validHourCount = 0;

        // 遍历每个小时时段
        for (Map.Entry<LocalDateTime, List<MeasIndHourRainfallSimpleRespVO>> entry : hourlyDataMap.entrySet()) {
            LocalDateTime hourTime = entry.getKey();
            List<MeasIndHourRainfallSimpleRespVO> hourData = entry.getValue();

            // 获取该小时有数据的设备列表
            List<Long> equipIdsWithData = hourData.stream()
                    .filter(data -> data.getRainfallVolume() != null && data.getRainfallVolume() > 0)
                    .map(MeasIndHourRainfallSimpleRespVO::getEquipId)
                    .distinct()
                    .toList();

            if (equipIdsWithData.isEmpty()) {
                continue;
            }

            // 计算该小时有数据设备的总集水面积
            double hourTotalArea = areaEquipList.stream()
                    .filter(equip -> equipIdsWithData.contains(equip.getEquipId()))
                    .mapToDouble(CatchmentAreaEquipDO::getArea)
                    .sum();

            if (hourTotalArea == 0.0) {
                continue;
            }

            // 计算该小时的加权平均降雨量
            double hourWeightedRainfall = 0.0;
            for (MeasIndHourRainfallSimpleRespVO data : hourData) {
                if (data.getRainfallVolume() != null && data.getRainfallVolume() > 0) {
                    CatchmentAreaEquipDO equip = equipMap.get(data.getEquipId());
                    if (equip != null && equip.getArea() != null) {
                        // 重新计算权重（基于该小时有数据的设备）
                        double weight = equip.getArea() / hourTotalArea;
                        hourWeightedRainfall += data.getRainfallVolume() * weight;
                    }
                }
            }

            totalWeightedRainfall += hourWeightedRainfall;
            validHourCount++;
        }

        if (validHourCount == 0) {
            return 0.0f;
        }

        // 使用PrecisionUtils保留3位小数
        return convert(totalWeightedRainfall, 3).floatValue();
    }

    public float calculateRainfallPeriodVolume(Long equipId,
                                               List<MeasIndHourRainfallSimpleRespVO> filterList) {
        if (equipId == null || filterList == null || filterList.isEmpty()) {
            return 0.0f;
        }

        // 计算该设备在时间段内的总降雨量
        double totalRainfall = filterList.stream()
                .mapToDouble(MeasIndHourRainfallSimpleRespVO::getRainfallVolume)
                .sum();

        // 使用PrecisionUtils保留3位小数
        return convert(totalRainfall, 3).floatValue();
    }

}

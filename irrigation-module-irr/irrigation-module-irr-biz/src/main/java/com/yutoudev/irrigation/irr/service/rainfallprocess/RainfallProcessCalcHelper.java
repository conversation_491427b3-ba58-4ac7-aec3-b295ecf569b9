package com.yutoudev.irrigation.irr.service.rainfallprocess;

import com.yutoudev.irrigation.irr.dal.dataobject.catchment.CatchmentAreaEquipDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.enums.RainfallProcessCalcTypeEnum;
import com.yutoudev.irrigation.irr.service.rainfallprocess.dto.RainfallProcessMergedDTO;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yutoudev.irrigation.framework.common.util.number.PrecisionUtils.convert;

/**
 * <AUTHOR>
 */
@Component
public class RainfallProcessCalcHelper {

    /**
     * 合并降雨数据
     *
     * @param hourRainfallList 小数降雨原始数据列表
     * @return 合并后的降雨数据
     */
    public List<RainfallProcessMergedDTO> merge(List<MeasIndHourDO> hourRainfallList,
                                                int calcType,
                                                List<CatchmentAreaEquipDO> areaEquipList
    ) {
        // 提取所有有降雨的时间点（所有设备的并集），并转换为实际降雨时间段
        List<RainfallPeriod> rainfallPeriods = hourRainfallList.stream()
                .filter(r -> r.getRainfallVolume() > 0)
                .map(r -> new RainfallPeriod(r.getHourTime().minusHours(1), r.getHourTime()))
                .distinct()
                .sorted(Comparator.comparing(RainfallPeriod::startTime))
                .toList();

        return mergeContinuousRainfallPeriods(hourRainfallList, rainfallPeriods, calcType, areaEquipList);

    }

    /**
     * 合并连续的降雨时间段
     *
     * @param sortedPeriods 已排序的降雨时间段列表
     * @return 合并后的降雨时间段列表
     */
    private List<RainfallProcessMergedDTO> mergeContinuousRainfallPeriods(
            List<MeasIndHourDO> originalList,
            List<RainfallPeriod> sortedPeriods,
            int calcType,
            List<CatchmentAreaEquipDO> areaEquipList
    ) {
        List<RainfallProcessMergedDTO> mergedPeriods = new ArrayList<>();
        if (sortedPeriods.isEmpty()) {
            return List.of();
        }

        // 初始化第一个降雨时间段
        RainfallPeriod currentPeriod = sortedPeriods.get(0);
        LocalDateTime mergedStart = currentPeriod.startTime();
        LocalDateTime mergedEnd = currentPeriod.endTime();
        int periodCounter = 1;
        String periodNameFormat = "{0}年，第{1}次";


        for (int i = 1; i < sortedPeriods.size(); i++) {
            RainfallPeriod nextPeriod = sortedPeriods.get(i);

            // 如果当前时间段的结束时间等于下一个时间段的开始时间，则合并
            if (mergedEnd.equals(nextPeriod.startTime())) {
                // 扩展结束时间
                mergedEnd = nextPeriod.endTime();
            } else {
                RainfallProcessMergedDTO mergedPeriod = new RainfallProcessMergedDTO();
                mergedPeriod.setPeriod(periodCounter);
                mergedPeriod.setPeriodName(MessageFormat.format(periodNameFormat,
                        String.valueOf(currentPeriod.startTime().getYear()), periodCounter));
                mergedPeriod.setStartTime(mergedStart);
                mergedPeriod.setEndTime(mergedEnd);
                mergedPeriod.setDuration(ChronoUnit.HOURS.between(mergedStart, mergedEnd));
                // 获取降雨数据列表
                LocalDateTime finalMergedStart = mergedStart;
                LocalDateTime finalMergedEnd = mergedEnd.plusHours(1);
                List<MeasIndHourDO> filterList = originalList.stream().filter(r ->
                        r.getHourTime().isAfter(finalMergedStart) && r.getHourTime().isBefore(finalMergedEnd)).toList();
                mergedPeriod.setRainfallDataList(filterList);
                float rainfallVolume;
                if (calcType == RainfallProcessCalcTypeEnum.BASIN.getType()) {
                    rainfallVolume = calculateRainfallPeriodVolume(areaEquipList, filterList, mergedStart, finalMergedEnd);
                } else {
                    rainfallVolume = calculateRainfallPeriodVolume(filterList.get(0).getEquipId(), filterList, mergedStart, finalMergedEnd);
                }
                mergedPeriod.setRainfallVolume(rainfallVolume);
                // 添加一个完整的合并时间段
                mergedPeriods.add(mergedPeriod);

                periodCounter++;

                // 开始新的时间段
                mergedStart = nextPeriod.startTime();
                mergedEnd = nextPeriod.endTime();
            }
        }

        RainfallProcessMergedDTO lastMergedPeriod = new RainfallProcessMergedDTO();
        lastMergedPeriod.setPeriod(periodCounter);
        lastMergedPeriod.setPeriodName(MessageFormat.format(periodNameFormat,
                String.valueOf(currentPeriod.startTime().getYear()), periodCounter));
        lastMergedPeriod.setStartTime(mergedStart);
        lastMergedPeriod.setEndTime(mergedEnd);
        lastMergedPeriod.setDuration(ChronoUnit.HOURS.between(mergedStart, mergedEnd));
        lastMergedPeriod.setRainfallVolume(0f);

        // 添加最后一个时间段
        mergedPeriods.add(lastMergedPeriod);

        return mergedPeriods;
    }

    public record RainfallPeriod(LocalDateTime startTime, LocalDateTime endTime) {
    }

    public float calculateRainfallPeriodVolume(List<CatchmentAreaEquipDO> areaEquipList,
                                               List<MeasIndHourDO> hourRainfallList,
                                               LocalDateTime startTime,
                                               LocalDateTime endTime) {
        if (areaEquipList == null || areaEquipList.isEmpty() || hourRainfallList == null || hourRainfallList.isEmpty()) {
            return 0.0f;
        }

        // 构建设备ID到集水面积设备的映射
        Map<Long, CatchmentAreaEquipDO> equipMap = areaEquipList.stream()
                .collect(Collectors.toMap(CatchmentAreaEquipDO::getEquipId, Function.identity()));

        // 过滤出时间范围内的降雨数据
        List<MeasIndHourDO> filteredData = hourRainfallList.stream()
                .filter(data -> data.getHourTime().isAfter(startTime) &&
                               data.getHourTime().isBefore(endTime) &&
                               equipMap.containsKey(data.getEquipId()) &&
                               data.getRainfallVolume() != null && data.getRainfallVolume() > 0)
                .toList();

        if (filteredData.isEmpty()) {
            return 0.0f;
        }

        // 计算总集水面积
        double totalArea = areaEquipList.stream()
                .filter(equip -> filteredData.stream().anyMatch(data -> data.getEquipId().equals(equip.getEquipId())))
                .mapToDouble(CatchmentAreaEquipDO::getArea)
                .sum();

        if (totalArea == 0.0) {
            return 0.0f;
        }

        // 计算加权平均降雨量
        double weightedSum = 0.0;
        for (MeasIndHourDO data : filteredData) {
            CatchmentAreaEquipDO equip = equipMap.get(data.getEquipId());
            if (equip != null && equip.getArea() != null) {
                double weight = equip.getArea() / totalArea;
                weightedSum += data.getRainfallVolume() * weight;
            }
        }

        // 使用PrecisionUtils保留1位小数
        return convert(weightedSum, 1).floatValue();
    }

    public float calculateRainfallPeriodVolume(Long equipId,
                                               List<MeasIndHourDO> filterList,
                                               LocalDateTime startTime,
                                               LocalDateTime endTime) {
        if (equipId == null || filterList == null || filterList.isEmpty()) {
            return 0.0f;
        }

        // 过滤出指定设备在时间范围内的降雨数据
        List<MeasIndHourDO> equipData = filterList.stream()
                .filter(data -> equipId.equals(data.getEquipId()) &&
                               data.getHourTime().isAfter(startTime) &&
                               data.getHourTime().isBefore(endTime) &&
                               data.getRainfallVolume() != null && data.getRainfallVolume() > 0)
                .toList();

        if (equipData.isEmpty()) {
            return 0.0f;
        }

        // 计算该设备在时间段内的总降雨量
        double totalRainfall = equipData.stream()
                .mapToDouble(MeasIndHourDO::getRainfallVolume)
                .sum();

        // 使用PrecisionUtils保留1位小数
        return convert(totalRainfall, 1).floatValue();
    }

}

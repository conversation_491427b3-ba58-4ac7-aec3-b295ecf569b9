package com.yutoudev.irrigation.irr.controller.admin.measfile.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.measfile.MeasFileDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 *
 * 量测设备上传照片文件list查询RequestVO
 * @description 管理后台-量测设备上传照片文件list查询RequestVO，参数和 MeasFilePageReqVO 是一致的
 * <AUTHOR>
 * @time 2024-05-21 10:42:01
 *
 */
@Data
public class MeasFileQueryReqVO extends QueryCriteria<MeasFileDO> {

    /**
     * 文件编号
     * 
     */
    private Long id;

    /**
     * 请求ID
     *
     */
    private String reqId;

    /**
     * 中心站ID
     *
     */
    private String centralId;

    /**
     * 设备ID
     * 
     */
    private String devId;

    /**
     * 文件名
     * 
     */
    private String name;

    /**
     * 文件路径
     * 
     */
    private String path;

    /**
     * 文件 URL
     * 
     */
    private String url;

    /**
     * 文件类型
     * 
     */
    private String type;

    /**
     * 文件后缀
     * 
     */
    private String suffix;

    /**
     * 文件大小
     * 
     */
    private Integer size;

    /**
     * MD5
     * 
     */
    private String md5;

    /**
     * 创建者
     * 
     */
    private String creator;

    /**
     * 创建时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    /**
     * 更新者
     * 
     */
    private String updater;

    /**
     * 更新时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;

    /**
     * 是否删除
     * 
     */
    private Boolean deleted;

}

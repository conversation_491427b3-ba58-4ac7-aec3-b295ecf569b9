package com.yutoudev.irrigation.irr.controller.admin.allocation;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.allocation.vo.modelarea.*;
import com.yutoudev.irrigation.irr.convert.allocation.AllocationModelAreaConvert;
import com.yutoudev.irrigation.irr.convert.allocation.AllocationModelAreaItemConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.allocation.AllocationModelAreaDO;
import com.yutoudev.irrigation.irr.dal.dataobject.allocation.AllocationModelAreaItemDO;
import com.yutoudev.irrigation.irr.service.allocation.AllocationModelAreaService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 * 输配水用水区域优化模型
 *
 * <AUTHOR>
 * @description 管理后台-输配水用水区域优化模型controller
 * @time 2024-12-01 09:56:45
 */
@RestController
@RequestMapping("/irr/allocation-model-area")
@Validated
public class AllocationModelAreaController {

    private static final String MODULE_NAME = "输配水用水区域优化模型";

    @Resource
    private AllocationModelAreaService<AllocationModelAreaDO> allocationModelAreaService;

    /**
     * 创建输配水用水区域优化模型
     *
     * @param createReqVO AllocationModelAreaCreateReqVO
     * @return CommonResult<Long> 返回ID
     * @description 单个对象保存
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody AllocationModelAreaCreateReqVO createReqVO) {
        return success(allocationModelAreaService.create(createReqVO));
    }

    /**
     * 批量创建输配水用水区域优化模型
     *
     * @param lists AllocationModelAreaCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 多个对象保存
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<AllocationModelAreaCreateReqVO> lists) {
        return success(allocationModelAreaService.createBatch(lists));
    }

    /**
     * 更新输配水用水区域优化模型
     *
     * @param updateReqVO AllocationModelAreaUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 单个对象修改
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody AllocationModelAreaUpdateReqVO updateReqVO) {
        allocationModelAreaService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新输配水用水区域优化模型
     *
     * @param lists 批量更新列表 AllocationModelAreaUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     * @description 批量更新
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<AllocationModelAreaUpdateReqVO> lists) {
        return success(allocationModelAreaService.updateBatch(lists));
    }

    /**
     * 删除输配水用水区域优化模型
     *
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID逻辑删除对象
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        allocationModelAreaService.delete(id);
        return success(true);
    }

    /**
     * 批量删除输配水用水区域优化模型
     *
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     * @description 根据ID列表逻辑删除对象
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(allocationModelAreaService.deleteBatch(ids));
    }

    /**
     * 获得输配水用水区域优化模型详情
     *
     * @param id 编号 Long
     * @return CommonResult<AllocationModelAreaDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<AllocationModelAreaDetailRespVO> get(@RequestParam("id") Long id) {
        AllocationModelAreaDO allocationModelArea = allocationModelAreaService.get(id);
        List<AllocationModelAreaItemDO> items = allocationModelAreaService.getItemByModelId(id);
        AllocationModelAreaDetailRespVO result = AllocationModelAreaConvert.INSTANCE.convertDetail(allocationModelArea);
        result.setItems(AllocationModelAreaItemConvert.INSTANCE.convertList(items));
        return success(result);
    }

    /**
     * 输配水用水区域优化模型列表
     *
     * @param queryReqVO 查询条件 AllocationModelAreaQueryReqVO
     * @return CommonResult<List < AllocationModelAreaRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<AllocationModelAreaRespVO>> getList(@RequestQueryParam AllocationModelAreaQueryReqVO queryReqVO) {
        List<AllocationModelAreaDO> list = allocationModelAreaService.getList(queryReqVO);
        return success(AllocationModelAreaConvert.INSTANCE.convertList(list));
    }

    /**
     * 输配水用水区域优化模型分页
     *
     * @param pageVO 查询条件 AllocationModelAreaPageReqVO
     * @return CommonResult<PageResult < AllocationModelAreaRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<AllocationModelAreaRespVO>> page(@RequestQueryParam AllocationModelAreaPageReqVO pageVO) {
        PageResult<AllocationModelAreaDO> pageResult = allocationModelAreaService.page(pageVO);
        return success(AllocationModelAreaConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出输配水用水区域优化模型Excel
     *
     * @param queryReqVO 查询条件 AllocationModelAreaExportReqVO
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam AllocationModelAreaExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<AllocationModelAreaDO> list = allocationModelAreaService.getList(queryReqVO);
        // 导出 Excel
        List<AllocationModelAreaExcelVO> datas = AllocationModelAreaConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "输配水用水区域优化模型", "xlsx"), queryReqVO.getExportSheetName(),
                AllocationModelAreaExcelVO.class, datas,
                queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入输配水用水区域优化模型模版下载
     *
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "输配水用水区域优化模型-导入模版.xls", "sheet1", AllocationModelAreaExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入输配水用水区域优化模型Excel
     *
     * @param file     导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:allocation-model-area:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<AllocationModelAreaExcelVO> list = ExcelUtils.read(file, AllocationModelAreaExcelVO.class);
        return success(allocationModelAreaService.importExcel(list, isUpdate));
    }
}
package com.yutoudev.irrigation.irr.controller.app.water;


import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.waterfee.vo.waterbill.WaterBillAmountReqVO;
import com.yutoudev.irrigation.irr.controller.admin.waterfee.vo.waterbill.WaterBillAmountRespVO;
import com.yutoudev.irrigation.irr.controller.admin.waterfee.vo.waterbill.WaterBillPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.waterfee.vo.waterbill.WaterBillRespVO;
import com.yutoudev.irrigation.irr.convert.waterfee.WaterBillConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.waterfee.WaterBillDO;
import com.yutoudev.irrigation.irr.service.waterfee.WaterBillService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;

/**
 * APP-用水账单
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/water-bill")
@Validated
public class AppWaterBillController {

    private static final String MODULE_NAME = "用水账单";

    @Resource
    private WaterBillService<WaterBillDO> waterBillService;

    /**
     * 用水账单分页
     */
    @GetMapping("/page")
    public CommonResult<PageResult<WaterBillRespVO>> page(@RequestQueryParam WaterBillPageReqVO pageVO) {
        PageResult<WaterBillDO> pageResult = waterBillService.page(pageVO);
        return success(WaterBillConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 合计
     */
    @GetMapping("/amount")
    public CommonResult<WaterBillAmountRespVO> amount(@RequestQueryParam WaterBillAmountReqVO reqVO) {
        WaterBillAmountRespVO respVO = waterBillService.amount(reqVO);
        return success(respVO);
    }
}

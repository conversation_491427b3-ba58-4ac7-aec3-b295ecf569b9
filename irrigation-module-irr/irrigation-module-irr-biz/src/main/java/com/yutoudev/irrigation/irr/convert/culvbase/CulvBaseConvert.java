package com.yutoudev.irrigation.irr.convert.culvbase;

import com.yutoudev.irrigation.framework.common.mapstruct.TypeConversion;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.culvbase.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.culvbase.CulvBaseDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 * 涵洞Convert
 * @description 管理后台-涵洞 mapstruct Convert
 * <AUTHOR>
 * @time 2024-05-11 13:50:24
 *
 */
@Mapper(uses = TypeConversion.class)
public interface CulvBaseConvert {

    CulvBaseConvert INSTANCE = Mappers.getMapper(CulvBaseConvert.class);

    /**
    * CreateReqVO转为DO
    * @param createReqVO createReqVO
    * @return DO
    */
    CulvBaseDO convert(CulvBaseCreateReqVO createReqVO);

    /**
    * 批量把 CreateReqVO 转为 DO
    * @param list CreateReqVO list
    * @return DO list
    */
    List<CulvBaseDO> convertCreateBatch(List<CulvBaseCreateReqVO> list);

    /**
    * UpdateReqVO转为DO
    * @param updateReqVO updateReqVO
    * @return DO
    */
    CulvBaseDO convert(CulvBaseUpdateReqVO updateReqVO);

    /**
    * 批量把 UpdateReqVO转为DO
    * @param list UpdateReqVO list
    * @return DO
    */
    List<CulvBaseDO> convertUpdateBatch(List<CulvBaseUpdateReqVO> list);

    /**
    * DO转化为RespVO
    * @param bean DO
    * @return RespVO
    */
    @Mappings({
            @Mapping(source = "createTime", target = "createTime", qualifiedByName = "convertLocalDateTimeToDate"),
            @Mapping(source = "updateTime", target = "updateTime", qualifiedByName = "convertLocalDateTimeToDate")
    })
    CulvBaseRespVO convert(CulvBaseDO bean);

    /**
    * DO转化为 DetailRespVO
    * @param bean DO
    * @return DetailRespVO
    */
    @Mappings({
            @Mapping(source = "createTime", target = "createTime", qualifiedByName = "convertLocalDateTimeToDate"),
    })
    CulvBaseDetailRespVO convertDetail(CulvBaseDO bean);

    @Mappings({
            @Mapping(source = "createTime", target = "createTime", qualifiedByName = "convertLocalDateTimeToDate"),
    })
    CulvBaseExcelVO convertCulvBaseExcelVO(CulvBaseDO bean);

    /**
    * 批量把DO转化为RespVO
    * @param list DO list
    * @return RespVO list
    */
    List<CulvBaseRespVO> convertList(List<CulvBaseDO> list);

    /**
    * 把PageResult中的DO转化为RespVO
    * @param page PageResult DO
    * @return PageResult RespVO
    */
    PageResult<CulvBaseRespVO> convertPage(PageResult<CulvBaseDO> page);

    /**
    * 批量把DO转化为ExcelVO
    * @param list DO list
    * @return ExcelVO list
    */
    List<CulvBaseExcelVO> convertExportExcel(List<CulvBaseDO> list);

    /**
    * 批量把ExcelVO转化为DO
    * @param list ExcelVO list
    * @return DO list
    */
    List<CulvBaseDO> convertImportExcel(List<CulvBaseExcelVO> list);

    /**
     * DO转化为SimpleRespVO
     * @param culvBaseDO
     * @return
     */
    CulvBaseSimpleRespVO convertSimpleVO(CulvBaseDO culvBaseDO);

    /**
     * 批量把DO转化为SimpleRespVO
     * @param list
     * @return
     */
    List<CulvBaseSimpleRespVO> convertSimpleList(List<CulvBaseDO> list);
}

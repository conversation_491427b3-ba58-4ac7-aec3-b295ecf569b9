package com.yutoudev.irrigation.irr.controller.admin.dambase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 大坝DetailResponseVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class DamBaseDetailRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 大坝代码
     */
    private String damCode;

    /**
     * 大坝名称
     */
    private String damName;

    /**
     * 起点经度
     */
    private Double startLng;

    /**
     * 起点纬度
     */
    private Double startLat;

    /**
     * 终点经度
     */
    private Double endLng;

    /**
     * 终点纬度
     */
    private Double endLat;

    /**
     * 泵站GIS
     */
    private String damLocGis;

    /**
     * 泵站所在位置
     */
    private String damLoc;

    /**
     * 是否主坝
     *
     * @mock （1是、2否
     */
    private Integer ifMainDam;

    /**
     * 工程等别
     */
    private Integer engGrade;

    /**
     * 大坝级别
     *
     * @mock 1:1级、2:2级、3:3级、4:4级、5:5级
     */
    private Integer damGrade;

    /**
     * 最大坝高
     */
    private Double maxHeight;

    /**
     * 坝顶长度
     */
    private Double topLength;

    /**
     * 坝顶宽度
     */
    private Double topWidth;

    /**
     * 坝顶高程
     */
    private Double topElevation;

    /**
     * 高程类型
     */
    private Integer elevationType;

    /**
     * 大坝材料类型
     *
     * @mock 1混凝土坝、2碾压混凝土坝、3浆砌石坝、4土坝、5堆石坝、6其它
     */
    private Integer materialType;

    /**
     * 大坝结构类型
     *
     * @mock 1重力坝、2拱坝、3支墩坝
     */
    private Integer structureType;

    /**
     * 工程建设情况
     */
    private Integer engStatus;

    /**
     * 防浪墙顶高程
     */
    private Double waveWallElevation;

    /**
     * 坝基地质
     *
     * @mock 1石灰岩、2砂砾石、3坚岩
     */
    private Integer geology;

    /**
     * 坝基防渗措施
     */
    private String seepageControl;

    /**
     * 建成时间
     */
    private LocalDate completionDate;

    /**
     * 桩号
     */
    private String mlgNum;

    /**
     * 水源地ID
     */
    private Long swhsId;

    /**
     * 水源地代码
     */
    private String swhsCode;

    /**
     * 水源地名称
     */
    private String swhsName;

    /**
     * 备注
     */
    private String note;

    /**
     * 运行状态
     * @mock 1:正常、2异常、3废弃
     */
    private Integer runStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;
}

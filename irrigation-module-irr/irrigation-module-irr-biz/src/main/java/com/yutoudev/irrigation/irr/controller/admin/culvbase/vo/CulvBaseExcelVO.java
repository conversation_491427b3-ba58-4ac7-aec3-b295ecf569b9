package com.yutoudev.irrigation.irr.controller.admin.culvbase.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.yutoudev.irrigation.framework.excel.core.annotations.DictFormat;
import com.yutoudev.irrigation.framework.excel.core.convert.DictConvert;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 涵洞ExcelVO
 *
 * <AUTHOR>
 * @description 管理后台-涵洞导出、导入ExcelVO
 * @time 2024-05-11 13:50:24
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class CulvBaseExcelVO {


    /**
     * ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 涵洞代码
     */
    @ExcelProperty("涵洞代码")
    private String culvCode;

    /**
     * 涵洞名称
     */
    @ExcelProperty("涵洞名称")
    private String culvName;

    /**
     * 涵洞经度
     */
    @ExcelProperty("涵洞经度")
    private Double culvLng;

    /**
     * 涵洞纬度
     */
    @ExcelProperty("涵洞纬度")
    private Double culvLat;

    /**
     * 涵洞所在位置
     */
    @ExcelProperty("涵洞所在位置")
    private String culvLoc;

    /**
     * 涵洞GIS
     *
     */
    @ExcelProperty("涵洞GIS")
    private String culvLocGis;

    /**
     * 管道断面形状
     *
     * @description 使用了DictConvert，根据irr_pipe_sec_type定义，确定该值范围
     */
    @ExcelProperty(value = "管道断面形状", converter = DictConvert.class)
    // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    @DictFormat("irr_pipe_sec_type")
    private Integer pipeSecType;

    /**
     * 管道净高
     */
    @ExcelProperty("管道净高")
    private Double pipeNetHeig;

    /**
     * 管道净宽
     */
    @ExcelProperty("管道净宽")
    private Double pipeNetWid;

    /**
     * 管道内径
     */
    @ExcelProperty("管道内径")
    private Double pipeIndi;

    /**
     * 孔数
     */
    @ExcelProperty("孔数")
    private Integer orifNum;

    /**
     * 涵洞过水能力
     */
    @ExcelProperty("涵洞过水能力")
    private Double culvWatProp;

    /**
     * 工程建设情况
     *
     * @description 使用了DictConvert，根据irr_eng_stat定义，确定该值范围
     */
    @ExcelProperty(value = "工程建设情况", converter = DictConvert.class)
    // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    @DictFormat("irr_eng_stat")
    private Integer engStat;

    /**
     * 开工时间
     */
    @ExcelProperty("开工时间")
    @ColumnWidth(20)
    private Date startDate;

    /**
     * 建成时间
     */
    @ExcelProperty("建成时间")
    @ColumnWidth(20)
    private Date compDate;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String note;

    /**
     * 水渠ID
     */
    @ExcelProperty("水渠ID")
    private Long chanId;

    /**
     * 水渠代码
     */
    @ExcelProperty("水渠代码")
    private String chanCode;

    /**
     * 水渠名称
     */
    @ExcelProperty("水渠名称")
    private String chanName;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;

    /**
     * 桩号
     */
    @ExcelProperty("桩号")
    private String mlgNum;
}

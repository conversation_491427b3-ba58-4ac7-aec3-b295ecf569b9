package com.yutoudev.irrigation.irr.convert.allocationplanyear;

import java.util.*;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yutoudev.irrigation.irr.controller.admin.allocationplanyear.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationplanyear.AllocationPlanYearDO;

/**
 *
 * 配水调度年计划Convert
 * @description 管理后台-配水调度年计划 mapstruct Convert
 * <AUTHOR>
 * @time 2024-07-23 14:54:37
 *
 */
@Mapper
public interface AllocationPlanYearConvert {

    AllocationPlanYearConvert INSTANCE = Mappers.getMapper(AllocationPlanYearConvert.class);

    /**
    * CreateReqVO转为DO
    * @param createReqVO createReqVO
    * @return DO
    */
    AllocationPlanYearDO convert(AllocationPlanYearCreateReqVO createReqVO);

    /**
    * 批量把 CreateReqVO 转为 DO
    * @param list CreateReqVO list
    * @return DO list
    */
    List<AllocationPlanYearDO> convertCreateBatch(List<AllocationPlanYearCreateReqVO> list);

    /**
    * UpdateReqVO转为DO
    * @param updateReqVO updateReqVO
    * @return DO
    */
    AllocationPlanYearDO convert(AllocationPlanYearUpdateReqVO updateReqVO);

    /**
    * 批量把 UpdateReqVO转为DO
    * @param list UpdateReqVO list
    * @return DO
    */
    List<AllocationPlanYearDO> convertUpdateBatch(List<AllocationPlanYearUpdateReqVO> list);

    /**
    * DO转化为RespVO
    * @param bean DO
    * @return RespVO
    */
    AllocationPlanYearRespVO convert(AllocationPlanYearDO bean);

    /**
    * DO转化为 DetailRespVO
    * @param bean DO
    * @return DetailRespVO
    */
    AllocationPlanYearDetailRespVO convertDetail(AllocationPlanYearDO bean);

    /**
    * 批量把DO转化为RespVO
    * @param list DO list
    * @return RespVO list
    */
    List<AllocationPlanYearRespVO> convertList(List<AllocationPlanYearDO> list);

    /**
    * 把PageResult中的DO转化为RespVO
    * @param page PageResult DO
    * @return PageResult RespVO
    */
    PageResult<AllocationPlanYearRespVO> convertPage(PageResult<AllocationPlanYearDO> page);

    /**
    * 批量把DO转化为ExcelVO
    * @param list DO list
    * @return ExcelVO list
    */
    List<AllocationPlanYearExcelVO> convertExportExcel(List<AllocationPlanYearDO> list);

    /**
    * 批量把ExcelVO转化为DO
    * @param list ExcelVO list
    * @return DO list
    */
    List<AllocationPlanYearDO> convertImportExcel(List<AllocationPlanYearExcelVO> list);
}

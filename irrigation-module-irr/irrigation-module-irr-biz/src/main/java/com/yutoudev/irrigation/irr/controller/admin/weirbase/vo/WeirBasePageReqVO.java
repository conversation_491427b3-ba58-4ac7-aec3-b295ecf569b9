package com.yutoudev.irrigation.irr.controller.admin.weirbase.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.weirbase.WeirBaseDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 *
 * 溢流堰分页RequestVO
 * @description 管理后台-溢流堰分页RequestVO
 * <AUTHOR>
 * @time 2024-05-12 20:27:43
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WeirBasePageReqVO extends PageCriteria<WeirBaseDO> {

    /**
     * 溢流堰代码
     * 
     */
    private String weirCode;

    /**
     * 溢流堰名称
     * 
     */
    private String weirName;

    /**
     * 溢流堰类型
     * 
     */
    private Integer weirType;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 运行状态
     * @mock 1:正常、2异常、3废弃
     */
    private Integer runStatus;

    /**
     * 水渠ID
     *
     */
    private Long chanId;

    /**
     * 创建时间
     * 
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

}

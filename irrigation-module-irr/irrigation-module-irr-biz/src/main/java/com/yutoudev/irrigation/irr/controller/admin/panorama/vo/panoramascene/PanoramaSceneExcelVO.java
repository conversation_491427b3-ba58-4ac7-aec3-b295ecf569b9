package com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramascene;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 全景图场景ExcelVO
 *
 * <AUTHOR>
 * @description 管理后台-全景图场景导出、导入ExcelVO
 * @time 2024-09-11 12:00:16
 */
@Data
@Accessors(chain = false)
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
@ColumnWidth(12)
public class PanoramaSceneExcelVO {


    /**
     * ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty("名称")
    private String name;

    /**
     * 缩略图路径
     */
    @ExcelProperty("缩略图路径")
    private String thumbFilePath;

    /**
     * 全景图路径
     */
    @ExcelProperty("全景图路径")
    private String panoFilePath;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    @ColumnWidth(20)
    private Date createTime;
}

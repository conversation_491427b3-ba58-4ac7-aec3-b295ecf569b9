package com.yutoudev.irrigation.irr.service.zonebase;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.zonebase.ZoneBaseDO;
import io.github.portaldalaran.talons.core.ITalonsService;

import jakarta.validation.Valid;
import java.util.List;

/**
 *
 * 片区Service接口
 * @description 管理后台-片区Service接口，对ITalonsService的扩展
 * <AUTHOR>
 * @time 2024-05-11 20:43:50
 *
 */
public interface ZoneBaseService <T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建片区
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid ZoneBaseCreateReqVO createReqVO);

    /**
    * 批量创建片区
    *
    * @param list 创建信息
    * @return 编号
    */
    boolean createBatch(@Valid List<ZoneBaseCreateReqVO> list);

    /**
     * 更新片区
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid ZoneBaseUpdateReqVO updateReqVO);

    /**
    * 批量更新片区
    *
    * @param list 更新信息
    */
    boolean updateBatch(@Valid List<ZoneBaseUpdateReqVO> list);

    /**
     * 删除片区
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
    * 批量删除片区
    *
    * @param ids 编号
    */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得片区
     *
     * @param id 编号
     * @return 片区
     */
    ZoneBaseDO get(Long id);
    /**
     * 片区列表
     *
     * @param ids 编号
     * @return 片区列表
     */
    List<ZoneBaseDO> getList(List<Long> ids);

    /**
     * 片区分页
     *
     * @param pageReqVO 分页查询
     * @return 片区分页
     */
    PageResult<ZoneBaseDO> page(ZoneBasePageReqVO pageReqVO);

    /**
    * 获得片区列表,
    *
    * @param queryReqVO 查询条件键值对
    * @return 片区列表
    */
    List<ZoneBaseDO> getList(ZoneBaseQueryReqVO queryReqVO);

    /**
    * 批量导入片区 excel
    *
    * @param importList     导入片区列表
    * @param isUpdateSupport 是否支持更新
    * @return 导入结果
    */
    ImportExcelRespVO importExcel(List<ZoneBaseExcelVO> importList, boolean isUpdateSupport);
    /**
     * <AUTHOR>
     * @MethodsName getByIds
     * @description 根据片区id查询列表
     * @param zoneIds
     */
    List<ZoneBaseRespVO> getByIds(List<Long> zoneIds);
}
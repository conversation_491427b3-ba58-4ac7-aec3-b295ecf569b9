package com.yutoudev.irrigation.irr.api.chanbase;

import com.yutoudev.irrigation.irr.api.chanbase.dto.ChanBaseDTO;
import com.yutoudev.irrigation.irr.convert.chanbase.ChanBaseConvert;
import com.yutoudev.irrigation.irr.service.chanbase.ChanBaseService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class ChanBaseApiImpl implements ChanBaseApi {

    @Resource
    private ChanBaseService chanBaseService;

    @Override
    public ChanBaseDTO getChanBaseById(Long id) {
        return ChanBaseConvert.INSTANCE.convert2DTO(chanBaseService.get(id));
    }
}

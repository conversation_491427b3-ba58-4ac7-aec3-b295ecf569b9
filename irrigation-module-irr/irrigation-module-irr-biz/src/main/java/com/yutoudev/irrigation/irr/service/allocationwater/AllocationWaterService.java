package com.yutoudev.irrigation.irr.service.allocationwater;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.manage.*;
import com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterEfficiencyRespVO;
import com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterPlanReqVO;
import com.yutoudev.irrigation.irr.controller.large.allocationWater.vo.WaterPlanRespVO;
import com.yutoudev.irrigation.irr.dal.dataobject.allocationwater.AllocationWaterDO;
import io.github.portaldalaran.talons.core.ITalonsService;

import jakarta.validation.Valid;

import java.util.List;
import java.util.Set;

/**
 * 配水管理Service接口
 *
 * <AUTHOR>
 * @description 管理后台-配水管理Service接口，对ITalonsService的扩展
 * @time 2024-11-27 21:47:07
 */
public interface AllocationWaterService<T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建配水管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid AllocationWaterCreateReqVO createReqVO);

    /**
     * 批量创建配水管理
     *
     * @param list 创建信息
     * @return 编号
     */
    boolean createBatch(@Valid List<AllocationWaterCreateReqVO> list);

    /**
     * 更新配水管理
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid AllocationWaterUpdateReqVO updateReqVO);

    /**
     * 批量更新配水管理
     *
     * @param list 更新信息
     */
    boolean updateBatch(@Valid List<AllocationWaterUpdateReqVO> list);

    /**
     * 删除配水管理
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
     * 批量删除配水管理
     *
     * @param ids 编号
     */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得配水管理
     *
     * @param id 编号
     * @return 配水管理
     */
    AllocationWaterDO get(Long id);

    /**
     * 配水管理列表
     *
     * @param ids 编号
     * @return 配水管理列表
     */
    List<AllocationWaterDO> getList(List<Long> ids);

    /**
     * 配水管理分页
     *
     * @param pageReqVO 分页查询
     * @return 配水管理分页
     */
    PageResult<AllocationWaterDO> page(AllocationWaterPageReqVO pageReqVO);

    /**
     * 获得配水管理列表,
     *
     * @param queryReqVO 查询条件键值对
     * @return 配水管理列表
     */
    List<AllocationWaterDO> getList(AllocationWaterQueryReqVO queryReqVO);

    /**
     * 批量导入配水管理 excel
     *
     * @param importList      导入配水管理列表
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    ImportExcelRespVO importExcel(List<AllocationWaterExcelVO> importList, boolean isUpdateSupport);


    /**
     * 配水优化
     *
     * @param reqVO
     * @return
     */
    List<AllocationWaterOptimizeRespVO> optimize(AllocationWaterOptimizeReqVO reqVO);

    /**
     * 确认配水优化
     *
     * @param reqVO
     */
    void confirmOptimize(AllocationWaterConfimOptimizeReqVO reqVO);

    /**
     * 按用水单元统计配水量
     *
     * @param queryReqVO
     * @return
     */
    List<AllocationWaterStatisticsRespVO> statisticsByChan(AllocationWaterStatisticsReqVO queryReqVO);

    /**
     * 按用水单元统计配水量
     *
     * @param queryReqVO
     * @return
     */
    List<AllocationWaterStatisticsRespVO> statisticsByUser(AllocationWaterStatisticsReqVO queryReqVO);

    /**
     * 按频率统计配水量
     *
     * @param queryReqVO
     * @return
     */
    List<AllocationWaterStatisticsRespVO> statisticsByFrequency(AllocationWaterStatisticsReqVO queryReqVO);

    /**
     * 执行管理
     *
     * @return
     */
    List<AllocationWaterSchedulingRespVO> scheduling();

    /**
     * 配水作物灌溉信息
     *
     * @param reqVO
     * @return
     */
    PageResult<AllocationWaterCropIrrigationRespVO> getCropIrrigations(AllocationWaterCropIrrigationQueryReqVO reqVO);

    /**
     * 读取当年配水调度支渠水汇总信息
     *
     * @return
     */
    List<WaterEfficiencyRespVO> getWaterEfficiency();

    /**
     * 读取配水调度支渠未执行中或者未执行（未完成）的配水计划
     * @param reqVO
     * @return
     */
    PageResult<WaterPlanRespVO> getWaterPlan(WaterPlanReqVO reqVO);

    /**
     * 根据计划ID更新配水调度方案
     * @param planIds
     */
    void updateByPlanId(Set<Long> planIds);
}
package com.yutoudev.irrigation.irr.controller.admin.bridgebase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yutoudev.irrigation.irr.controller.admin.chanbase.vo.ChanBaseRespVO;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 *
 * 桥DetailResponseVO
 * @description 管理后台-桥DetailResponseVO
 * <AUTHOR>
 * @time 2024-05-12 20:34:18
 *
 */
@Data
@ToString(callSuper = true)
public class BridgeBaseDetailRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 桥代码
     * 
     */
    private String bridgeCode;

    /**
     * 桥名称
     * 
     */
    private String bridgeName;

    /**
     * 起点经度
     * 
     */
    private Double startLng;

    /**
     * 起点纬度
     * 
     */
    private Double startLat;

    /**
     * 终点经度
     * 
     */
    private Double endLng;

    /**
     * 终点纬度
     * 
     */
    private Double endLat;

    /**
     * 桥GIS
     *
     */
    private String bridgeLocGis;

    /**
     * 桥所在位置
     * 
     */
    private String bridgeLoc;

    /**
     * 桥长度
     * 
     */
    private Double bridgeLen;

    /**
     * 桥面宽度
     * 
     */
    private Double bridgeDeckWidth;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 运行状态
     * @mock 1:正常、2异常、3废弃
     */
    private Integer runStatus;

    /**
     * 水渠ID
     * 
     */
    private Long chanId;

    /**
     * 水渠代码
     * 
     */
    private String chanCode;

    /**
     * 水渠名称
     * 
     */
    private String chanName;

    /**
     * 水渠
     * 
     */
    private ChanBaseRespVO chan;

    /**
     * 创建时间
     * 
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    /**
     * 桩号
     */
    private String mlgNum;
}

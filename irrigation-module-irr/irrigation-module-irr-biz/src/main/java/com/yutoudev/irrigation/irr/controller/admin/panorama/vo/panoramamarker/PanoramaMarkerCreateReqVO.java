package com.yutoudev.irrigation.irr.controller.admin.panorama.vo.panoramamarker;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * 全景图场景标记创建RequestVO
 *
 * <AUTHOR>
 * @description 管理后台-全景图场景标记创建RequestVO
 * @time 2024-09-11 12:00:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PanoramaMarkerCreateReqVO extends PanoramaMarkerBaseVO {

}

package com.yutoudev.irrigation.irr.controller.admin.inletanalysisdetailexecute.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;


/**
 *
 * 来水分析明细执行记录更新RequestVO
 * @description 管理后台-来水分析明细执行记录更新RequestVO
 * <AUTHOR>
 * @time 2024-08-09 10:49:53
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InletAnalysisDetailExecuteUpdateReqVO extends InletAnalysisDetailExecuteBaseVO {

    /**
     * ID
     * 
     */
    @NotNull(message = "ID不能为空")
    private Long id;
}
package com.yutoudev.irrigation.irr.service.guard.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.guard.vo.*;
import com.yutoudev.irrigation.irr.convert.guard.IrrOnGuardConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.guard.IrrOnGuardDO;
import com.yutoudev.irrigation.irr.dal.mysql.floodplan.IrrOnGuardMapper;
import com.yutoudev.irrigation.irr.service.guard.IrrOnGuardService;
import com.yutoudev.irrigation.module.system.api.user.AdminUserApi;
import com.yutoudev.irrigation.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.IrrOnGuardErrorCode.IRR_ON_GUARD_NOT_EXISTS;

@Service
@Validated
public class IrrOnGuardServiceImpl implements IrrOnGuardService {

    @Resource
    private IrrOnGuardMapper irrOnGuardMapper;

    @Resource
    private AdminUserApi userApi;
    
    @Override
    public Long createIrrOnGuard(IrrOnGuardCreateReqVO createReqVO) {
        // 插入
        IrrOnGuardDO irrOnGuard = IrrOnGuardConvert.INSTANCE.convert(createReqVO);
        irrOnGuardMapper.insert(irrOnGuard);
        // 返回
        return irrOnGuard.getId();
    }

    @Override
    public void updateIrrOnGuard(IrrOnGuardUpdateReqVO updateReqVO) {
        // 校验存在
        validateIrrOnGuardExists(updateReqVO.getId());
        // 更新
        IrrOnGuardDO updateObj = IrrOnGuardConvert.INSTANCE.convert(updateReqVO);
        irrOnGuardMapper.updateById(updateObj);
    }

    @Override
    public void deleteIrrOnGuard(Long id) {
        // 校验存在
        validateIrrOnGuardExists(id);
        // 删除
        irrOnGuardMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIrrOnGuard(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 批量删除
        irrOnGuardMapper.deleteBatchIds(ids);
    }

    private void validateIrrOnGuardExists(Long id) {
        if (irrOnGuardMapper.selectById(id) == null) {
            throw exception(IRR_ON_GUARD_NOT_EXISTS);
        }
    }

    @Override
    public IrrOnGuardRespVO getIrrOnGuard(Long id) {
        IrrOnGuardDO irrOnGuard = irrOnGuardMapper.selectById(id);
        return IrrOnGuardConvert.INSTANCE.convert(irrOnGuard);
    }

    @Override
    public PageResult<IrrOnGuardRespVO> getIrrOnGuardPage(IrrOnGuardPageReqVO pageReqVO) {
        PageResult<IrrOnGuardDO> pageResult = irrOnGuardMapper.selectPage(pageReqVO);
        PageResult<IrrOnGuardRespVO> page = IrrOnGuardConvert.INSTANCE.convertPage(pageResult);
        formatUser(page.getList());
        return page;
    }

    @Override
    public List<IrrOnGuardRespVO> getIrrOnGuardList(IrrOnGuardPageReqVO pageReqVO) {
        List<IrrOnGuardDO> list = irrOnGuardMapper.selectListByDate(pageReqVO.getDate());
        return formatUser(IrrOnGuardConvert.INSTANCE.convertList(list));
    }

    private List<IrrOnGuardRespVO> formatUser(List<IrrOnGuardRespVO> list) {
        // 获取所有用户id
        Set<Long> userIds = new HashSet<>();
        for (IrrOnGuardRespVO vo : list) {
            // 从guardLeader字段获取用户id
            if (StrUtil.isNotEmpty(vo.getGuardLeader())) {
                String[] leaderIds = vo.getGuardLeader().split(",");
                for (String id : leaderIds) {
                    userIds.add(Long.valueOf(id));
                }
            }
            // 从guardPerson字段获取用户id 
            if (StrUtil.isNotEmpty(vo.getGuardPerson())) {
                String[] personIds = vo.getGuardPerson().split(",");
                for (String id : personIds) {
                    userIds.add(Long.valueOf(id));
                }
            }
        }
        List<AdminUserRespDTO> users = userApi.getUserList(userIds);
        // 格式化用户信息
        for (IrrOnGuardRespVO vo : list) {
            // 处理值班领导名称
            if (StrUtil.isNotEmpty(vo.getGuardLeader())) {
                String[] leaderIds = vo.getGuardLeader().split(",");
                StringBuilder leaderNames = new StringBuilder();
                for (String id : leaderIds) {
                    for (AdminUserRespDTO user : users) {
                        if (user.getId().equals(Long.valueOf(id))) {
                            if (leaderNames.length() > 0) {
                                leaderNames.append(",");
                            }
                            leaderNames.append(user.getNickname());
                            break;
                        }
                    }
                }
                vo.setGuardLeaderName(leaderNames.toString());
            }
            
            // 处理值班人员名称
            if (StrUtil.isNotEmpty(vo.getGuardPerson())) {
                String[] personIds = vo.getGuardPerson().split(",");
                StringBuilder personNames = new StringBuilder();
                for (String id : personIds) {
                    for (AdminUserRespDTO user : users) {
                        if (user.getId().equals(Long.valueOf(id))) {
                            if (personNames.length() > 0) {
                                personNames.append("，");
                            }
                            personNames.append(user.getNickname());
                            break;
                        }
                    }
                }
                vo.setGuardPersonName(personNames.toString());
            }
        }
        return list;
    }
} 
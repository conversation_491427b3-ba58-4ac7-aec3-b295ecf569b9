package com.yutoudev.irrigation.irr.service.equipsection;

import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.irr.controller.admin.equipsection.vo.*;
import com.yutoudev.irrigation.irr.convert.equipsection.EquipSectionConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipsection.EquipSectionDO;
import com.yutoudev.irrigation.irr.dal.mysql.equipsection.EquipSectionMapper;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.BasicSetErrorCodeConstants.*;

/**
 *
 * 设备断面设置Service实现类
 * @description 管理后台-设备断面设置Service实现类
 * <AUTHOR>
 * @time 2024-08-05 23:32:40
 *
 */
@Service
@Validated
public class EquipSectionServiceImpl extends TalonsServiceImpl<EquipSectionMapper, EquipSectionDO> implements EquipSectionService<EquipSectionDO> {

    @Resource
    private EquipSectionMapper equipSectionMapper;

    @Resource
    private TalonsHelper talonsHelper;

    @Override
    public void checkField(EquipSectionDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(EquipSectionCreateReqVO createReqVO) {
        // 插入
        EquipSectionDO equipSection = EquipSectionConvert.INSTANCE.convert(createReqVO);
        this.save(equipSection, true);
        // 返回
        return equipSection.getId();
    }

    @Override
    public boolean createBatch(List<EquipSectionCreateReqVO> list) {

        List<EquipSectionDO> saveList = EquipSectionConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        }else{
            throw exception(GATE_SECTION_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public void update(EquipSectionUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateEquipSectionExists(updateReqVO.getId());
        // 更新
        EquipSectionDO updateObj = EquipSectionConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }


    @Override
    public boolean updateBatch(List<EquipSectionUpdateReqVO> list) {

        List<EquipSectionDO> updateList = EquipSectionConvert.INSTANCE.convertUpdateBatch(list);

        for(EquipSectionDO tempDO: updateList){
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateEquipSectionExists(tempDO.getId());
        }

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(GATE_SECTION_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateEquipSectionExists(id);
        // 删除
        this.removeById(id, true);
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(GATE_SECTION_DELETE_BATCH_ERROR);
        }
    }

    private void validateEquipSectionExists(Long id) {
        if (equipSectionMapper.selectById(id) == null) {
            throw exception(GATE_SECTION_NOT_EXISTS);
        }
    }

    @Override
    public EquipSectionDO get(Long id) {
        return this.getById(id, true);
    }
    @Override
    public List<EquipSectionDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public PageResult<EquipSectionDO> page(EquipSectionPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<EquipSectionDO> queryBuilder = new QueryCriteriaWrapperBuilder<EquipSectionDO>() { };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<EquipSectionDO> pageResult = equipSectionMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
            talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<EquipSectionDO> getList(EquipSectionQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<EquipSectionDO> queryBuilder = new QueryCriteriaWrapperBuilder<EquipSectionDO>() { };
        queryBuilder.build(queryReqVO);

        List<EquipSectionDO> result = equipSectionMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<EquipSectionExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(GATE_SECTION_IMPORT_LIST_IS_EMPTY);
        }

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<EquipSectionDO> saveList = EquipSectionConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            EquipSectionDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, EquipSectionExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }
}

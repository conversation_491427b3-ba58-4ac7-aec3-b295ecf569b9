package com.yutoudev.irrigation.irr.controller.admin.meascleanconfig.vo;

import com.yutoudev.irrigation.irr.dal.dataobject.meascleanconfig.MeasCleanConfigDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 *
 * 数据清洗配置分页RequestVO
 * @description 管理后台-数据清洗配置分页RequestVO
 * <AUTHOR>
 * @time 2024-08-17 16:53:06
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeasCleanConfigPageReqVO extends PageCriteria<MeasCleanConfigDO> {

    /**
     * 所属表类型
     * 
     */
    private String tableType;

    /**
     * 设备类型
     * 
     */
    private Integer devType;

    /**
     * 雨量算法
     * 
     */
    private Integer rainfallAlgorithm;

    /**
     * 指标ID
     * 
     */
    private Integer indTypeId;

}

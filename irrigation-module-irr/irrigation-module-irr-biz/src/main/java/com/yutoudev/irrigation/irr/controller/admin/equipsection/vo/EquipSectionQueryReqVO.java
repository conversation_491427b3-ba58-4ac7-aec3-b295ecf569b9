package com.yutoudev.irrigation.irr.controller.admin.equipsection.vo;

import lombok.*;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.equipsection.EquipSectionDO;


/**
 *
 * 设备断面设置list查询RequestVO
 * @description 管理后台-设备断面设置list查询RequestVO，参数和 EquipSectionPageReqVO 是一致的
 * <AUTHOR>
 * @time 2024-08-05 23:32:40
 *
 */
@Data
public class EquipSectionQueryReqVO extends QueryCriteria<EquipSectionDO> {

    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 类型
     * @mock 1U型 2梯型 3矩形
     */
    private Integer type;

    /**
     * 糙率
     * 
     */
    private Double roughness;

    /**
     * 墙面类型
     * @mock 1扭面翼墙 2平翼墙 3八字翼墙 4平等侧翼墙
     */
    private Integer wallType;

    /**
     * 左边坡系数
     * 
     */
    private Double leftSlope;

    /**
     * 右边坡系数
     * 
     */
    private Double rightSlope;

    /**
     * 底宽
     * @mock 米
     */
    private Double bottomWidth;

    /**
     * 圆半径
     * @mock 米
     */
    private Double radius;

    /**
     * 跌坎高度
     * @mock 米
     */
    private Double stepDownFloor;

    /**
     * 水力坡度
     * 
     */
    private Double waterGrade;

}

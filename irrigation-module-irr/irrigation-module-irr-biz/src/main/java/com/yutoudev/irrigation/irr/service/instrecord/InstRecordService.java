package com.yutoudev.irrigation.irr.service.instrecord;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.irr.controller.admin.instrecord.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.instrecord.InstRecordDO;
import io.github.portaldalaran.talons.core.ITalonsService;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 设备指令下发记录Service接口
 *
 * <AUTHOR>
 * @description 管理后台-设备指令下发记录Service接口，对ITalonsService的扩展
 * @time 2024-05-20 15:01:19
 */
public interface InstRecordService<T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建设备指令下发记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid InstRecordCreateReqVO createReqVO);

    /**
     * 批量创建设备指令下发记录
     *
     * @param list 创建信息
     * @return 编号
     */
    boolean createBatch(@Valid List<InstRecordCreateReqVO> list);

    /**
     * 更新设备指令下发记录
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid InstRecordUpdateReqVO updateReqVO);

    /**
     * 根据ID列表更新指令状态
     *
     * @param ids 操作指令ID列表
     */
    void updateSuccessByIds(List<Long> ids, Integer resultCode);

    /**
     * 批量更新设备指令下发记录
     *
     * @param list 更新信息
     */
    boolean updateBatch(@Valid List<InstRecordUpdateReqVO> list);

    /**
     * 删除设备指令下发记录
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
     * 批量删除设备指令下发记录
     *
     * @param ids 编号
     */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得设备指令下发记录
     *
     * @param id 编号
     * @return 设备指令下发记录
     */
    InstRecordDO get(Long id);

    /**
     * 根据请求ID查询操作记录
     *
     * @param reqId    请求ID
     * @param instType 指令类型
     * @return 设备指令下发记录
     */
    InstRecordDO getByReqIdAndInstType(String reqId, Integer instType);

    /**
     * 根据中心站ID和设备ID查询操作记录
     *
     * @param centralId 中心站ID
     * @param devId     设备ID
     * @param instType  指令类型
     * @param timeout   超时时间
     * @return 设备指令下发记录
     */
    List<InstRecordDO> getCurrentDevOperation(String centralId, String devId, Integer instType, Integer timeout);

    /**
     * 设备指令下发记录列表
     *
     * @param ids 编号
     * @return 设备指令下发记录列表
     */
    List<InstRecordDO> getList(List<Long> ids);

    /**
     * 设备指令下发记录分页
     *
     * @param pageReqVO 分页查询
     * @return 设备指令下发记录分页
     */
    PageResult<InstRecordDO> page(InstRecordPageReqVO pageReqVO);

    /**
     * 获得设备指令下发记录列表,
     *
     * @param queryReqVO 查询条件键值对
     * @return 设备指令下发记录列表
     */
    List<InstRecordDO> getList(InstRecordQueryReqVO queryReqVO);

    /**
     * 批量导入设备指令下发记录 excel
     *
     * @param importList      导入设备指令下发记录列表
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    ImportExcelRespVO importExcel(List<InstRecordExcelVO> importList, boolean isUpdateSupport);

    /**
     * 获取该设备最新的一条操作记录
     *
     * @param centralId 中心站ID
     * @param devId     设备id
     * @param equipType 设备类型
     * @return 导入结果
     */
    InstRecordRespVO getLatestInstRecord(String centralId, String devId, Integer equipType);

    /**
     * 获取该设备最新的一条本类型操作指令记录
     *
     * @param centralId 中心站ID
     * @param devId     设备id
     * @param instType  指令类型
     * @param statuses  指令状态集合
     * @return 导入结果
     */
    InstRecordRespVO getLatestInstRecordByInstType(String centralId, String devId, Integer instType, List<Integer> statuses);

    /**
     * 获取该设备最新的一条本类型操作指令记录
     *
     * @param centralId 中心站ID
     * @param devId     设备id
     * @return 导入结果
     */
    List<InstRecordRespVO> getLatestInstRecordByControlTypes(String centralId, String devId);

    /**
     * 获取该设备最新的一条控制指令记录
     *
     * @param centralId 中心站ID
     * @param devId     设备id
     * @return 导入结果
     */
    InstRecordRespVO getLatestInstRecordForDisplay(String centralId, String devId, Integer equipType);
}
package com.yutoudev.irrigation.irr.dal.dataobject.tunnelbase;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.chanbase.ChanBaseDO;
import com.yutoudev.irrigation.irr.dal.mysql.chanbase.ChanBaseMapper;
import com.yutoudev.irrigation.irr.enums.BuildingEngStatsEnum;
import io.github.portaldalaran.talons.annotation.ManyToOne;
import lombok.*;

import java.util.Date;

/**
 * 隧道基础信息DO
 *
 * <AUTHOR>
 * @description 管理后台-隧道基础信息数据库对象
 * @time 2024-05-10 21:18:51
 */
@TableName(value = "irr_tunnel_base", autoResultMap = true)
@KeySequence("irr_tunnel_base_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TunnelBaseDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 隧道代码
     */
    private String tunnelCode;

    /**
     * 隧道名称
     */
    private String tunnelName;

    /**
     * 起点经度
     */
    private Double startLng;

    /**
     * 起点纬度
     */
    private Double startLat;

    /**
     * 终点经度
     */
    private Double endLng;

    /**
     * 终点纬度
     */
    private Double endLat;

    /**
     * 隧道GIS
     */
    private String tunnelLocGis;

    /**
     * 隧道所在位置
     */
    private String tunnelLoc;

    /**
     * 隧道类型
     * <p>
     * <p>
     * 枚举 {@link //TODO irr_tunnel_type 对应的类}
     */
    private Integer tunnelType;

    /**
     * 洞径
     */
    private Double tunnelDiameter;

    /**
     * 转弯弯曲半径
     */
    private Double bendingRadius;

    /**
     * 进口高程
     */
    private Double entranceAltitude;

    /**
     * 进口高程
     */
    private Double outletAltitude;

    /**
     * 设计流量
     */
    private Long desFlux;

    /**
     * 开工时间
     */
    private Date startDate;

    /**
     * 建成时间
     */
    private Date compDate;

    /**
     * @apiNote 多对一关联, 用于查询时
     */
    @TableField(exist = false)
    @ManyToOne(targetEntity = ChanBaseDO.class, targetMapper = ChanBaseMapper.class)
    private ChanBaseDO chan;

    /**
     * 水渠ID
     */
    private Long chanId;

    /**
     * 水渠代码
     */
    private String chanCode;

    /**
     * 水渠名称
     */
    private String chanName;

    /**
     * 备注
     */
    private String note;

    /**
     * 工程建设情况
     * 枚举 {@link BuildingEngStatsEnum}
     */
    private Integer engStat;

    /**
     * 运行状态
     * @mock 1:正常、2异常、3废弃
     */
    private Integer runStatus;

    /**
     * 桩号
     */
    private String mlgNum;

}

package com.yutoudev.irrigation.irr.dal.mysql.history;

import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.mybatis.core.mapper.BaseMapperX;
import com.yutoudev.irrigation.irr.controller.admin.history.vo.evaporationday.HistoryEvaporationDayPageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.history.vo.evaporationday.HistoryEvaporationDayQueryReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryEvaporationDayDO;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 历史日均蒸散发量 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HistoryEvaporationDayMapper extends BaseMapperX<HistoryEvaporationDayDO> {
    default PageResult<HistoryEvaporationDayDO> selectPage(HistoryEvaporationDayPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<HistoryEvaporationDayDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<HistoryEvaporationDayDO>() {
        };
        queryCriteriaWrapperBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        return selectPage(pageParam, queryCriteriaWrapperBuilder.getQueryWrapper());
    }

    default List<HistoryEvaporationDayDO> selectList(HistoryEvaporationDayQueryReqVO reqVO) {
        QueryCriteriaWrapperBuilder<HistoryEvaporationDayDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<HistoryEvaporationDayDO>() {
        };
        queryCriteriaWrapperBuilder.build(reqVO);
        return selectList(queryCriteriaWrapperBuilder.getQueryWrapper());
    }

}

package com.yutoudev.irrigation.irr.controller.admin.chanbuilding;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.chanbuilding.vo.*;
import com.yutoudev.irrigation.irr.convert.chanbuilding.ChanBuildingConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.chanbuilding.ChanBuildingDO;
import com.yutoudev.irrigation.irr.service.chanbuilding.ChanBuildingService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.*;


/**
 *
 * 水渠建筑物关联
 * @description 管理后台-水渠建筑物关联controller
 * <AUTHOR>
 * @time 2024-05-23 09:16:28
 *
 */
@RestController
@RequestMapping("/irr/chan-building")
@Validated
public class ChanBuildingController {

    private static final String MODULE_NAME = "水渠建筑物关联";

    @Resource
    private ChanBuildingService<ChanBuildingDO> chanBuildingService;

    /**
     * 创建水渠建筑物关联
     * @description 单个对象保存
     * @param createReqVO ChanBuildingCreateReqVO
     * @return CommonResult<Long> 返回ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE, type = CREATE)
    public CommonResult<Long> create(@Valid @RequestBody ChanBuildingCreateReqVO createReqVO) {
        return success(chanBuildingService.create(createReqVO));
    }

    /**
     * 批量创建水渠建筑物关联
     * @description 多个对象保存
     * @param lists  ChanBuildingCreateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PostMapping("/createBatch")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:create')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.CREATE_BATCH, type = CREATE)
    public CommonResult<Boolean> createBatch(@Valid @RequestBody List<ChanBuildingCreateReqVO> lists) {
        return success(chanBuildingService.createBatch(lists));
    }

    /**
     * 更新水渠建筑物关联
     * @description 单个对象修改
     * @param updateReqVO ChanBuildingUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE, type = UPDATE)
    public CommonResult<Boolean> update(@Valid @RequestBody ChanBuildingUpdateReqVO updateReqVO) {
        chanBuildingService.update(updateReqVO);
        return success(true);
    }


    /**
     * 批量更新水渠建筑物关联
     * @description 批量更新
     * @param lists 批量更新列表 ChanBuildingUpdateReqVO
     * @return CommonResult<Boolean> 成功/失败
     */
    @PutMapping("/updateBatch")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:update')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.UPDATE_BATCH, type = UPDATE)
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<ChanBuildingUpdateReqVO> lists) {
        return success(chanBuildingService.updateBatch(lists));
    }

    /**
     * 删除水渠建筑物关联
     * @description 根据ID逻辑删除对象
     * @param id 编号 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE, type = DELETE)
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        chanBuildingService.delete(id);
        return success(true);
    }

    /**
     * 批量删除水渠建筑物关联
     * @description 根据ID列表逻辑删除对象
     * @param ids 编号列表 Long
     * @return CommonResult<Boolean> 成功/失败
     */
    @DeleteMapping("/deleteBatch")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:delete')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.DELETE_BATCH, type = DELETE)
    public CommonResult<Boolean> deleteBatch(@RequestParam List<Long> ids) {
        return success(chanBuildingService.deleteBatch(ids));
    }

    /**
     * 获得水渠建筑物关联详情
     * @description 根据ID取对象所有字段
     * @param id 编号 Long
     * @return CommonResult<ChanBuildingDetailRespVO> 详情响应VO
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasAnyPermissions('irr:chan-building:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.GET, type = GET)
    public CommonResult<ChanBuildingDetailRespVO> get(@RequestParam("id") Long id) {
        ChanBuildingDO chanBuilding = chanBuildingService.get(id);
        return success(ChanBuildingConvert.INSTANCE.convertDetail(chanBuilding));
    }

    /**
     * 水渠建筑物关联列表
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param queryReqVO 查询条件 ChanBuildingQueryReqVO
     * @return CommonResult<List<ChanBuildingRespVO>> 列表响应VO
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasAnyPermissions('irr:chan-building:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<ChanBuildingRespVO>> getList(@RequestQueryParam ChanBuildingQueryReqVO queryReqVO) {
        List<ChanBuildingDO> list = chanBuildingService.getList(queryReqVO);
        return success(ChanBuildingConvert.INSTANCE.convertList(list));
    }

    /**
     * 水渠建筑物关联分页
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     * @param pageVO 查询条件 ChanBuildingPageReqVO
     * @return CommonResult<PageResult<ChanBuildingRespVO>> 列表响应VO
     */
    @GetMapping("/page")
    @PreAuthorize("@ss.hasAnyPermissions('irr:chan-building:query','common:query')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.PAGE, type = GET)
    public CommonResult<PageResult<ChanBuildingRespVO>> page(@RequestQueryParam ChanBuildingPageReqVO pageVO) {
        PageResult<ChanBuildingDO> pageResult = chanBuildingService.page(pageVO);
        return success(ChanBuildingConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 导出水渠建筑物关联Excel
     * @description 根据查询条件分布导出指定列(可设置只导出或者排除哪些列)的excel，查询条件以RequestVO为基准，可以使用talons查询组装器扩展。完成后直接返回文件流。
     * @param queryReqVO 查询条件 ChanBuildingExportReqVO
     * @download
     */
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:export')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.EXPORT_EXCEL, type = EXPORT)
    public void exportExcel(@RequestQueryParam ChanBuildingExportReqVO queryReqVO, HttpServletResponse response) throws IOException {
        List<ChanBuildingDO> list = chanBuildingService.getList(queryReqVO);
        // 导出 Excel
        List<ChanBuildingExcelVO> datas = ChanBuildingConvert.INSTANCE.convertExportExcel(list);
        ExcelUtils.write(response, ExcelUtils.getFileName(queryReqVO.getExportFileName(), "水渠建筑物关联", "xlsx"), queryReqVO.getExportSheetName(),
                                                    ChanBuildingExcelVO.class, datas,
                                                    queryReqVO.getExportExcludeColumns(), queryReqVO.getExportIncludeColumns());
    }

    /**
     * 导入水渠建筑物关联模版下载
     * @description 下载导入的excel模版。
     * @download
     */
    @GetMapping("/import-excel-template")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = GET)
    public void importExcelTemplate(HttpServletResponse response) throws IOException {
        // 导出 Excel
        ExcelUtils.write(response, "水渠建筑物关联-导入模版.xls", "sheet1", ChanBuildingExcelVO.class, new ArrayList<>());
    }

    /**
     * 导入水渠建筑物关联Excel
     * @description 执行完导入后，返回创建成功/更新成功/导入失败的列表
     * @param file 导入的excel文件 MultipartFile
     * @param isUpdate 是否支持更新，默认为true Boolean
     * @return CommonResult<ImportExcelRespVO> 导入响应VO
     */
    @PostMapping("/import-excel")
    @PreAuthorize("@ss.hasPermission('irr:chan-building:import')")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.IMPORT_EXCEL, type = IMPORT)
    public CommonResult<ImportExcelRespVO> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(value = "isUpdate", required = false, defaultValue = "true") Boolean isUpdate) throws IOException {
        List<ChanBuildingExcelVO> list = ExcelUtils.read(file, ChanBuildingExcelVO.class);
        return success(chanBuildingService.importExcel(list, isUpdate));
    }
}
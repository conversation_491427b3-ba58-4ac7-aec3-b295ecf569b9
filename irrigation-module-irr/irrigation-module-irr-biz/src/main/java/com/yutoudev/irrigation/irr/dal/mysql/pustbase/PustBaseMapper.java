package com.yutoudev.irrigation.irr.dal.mysql.pustbase;

import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.mybatis.core.mapper.BaseMapperX;
import com.yutoudev.irrigation.irr.controller.admin.pustbase.vo.PustBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.pustbase.vo.PustBaseQueryReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.pustbase.PustBaseDO;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 泵站基础信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PustBaseMapper extends BaseMapperX<PustBaseDO> {
    default PageResult<PustBaseDO> selectPage(PustBasePageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<PustBaseDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<PustBaseDO>() {
        };
        queryCriteriaWrapperBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        return selectPage(pageParam, queryCriteriaWrapperBuilder.getQueryWrapper());
    }

    default List<PustBaseDO> selectList(PustBaseQueryReqVO reqVO) {
        QueryCriteriaWrapperBuilder<PustBaseDO> queryCriteriaWrapperBuilder = new QueryCriteriaWrapperBuilder<PustBaseDO>() {
        };
        queryCriteriaWrapperBuilder.build(reqVO);
        return selectList(queryCriteriaWrapperBuilder.getQueryWrapper());
    }

}

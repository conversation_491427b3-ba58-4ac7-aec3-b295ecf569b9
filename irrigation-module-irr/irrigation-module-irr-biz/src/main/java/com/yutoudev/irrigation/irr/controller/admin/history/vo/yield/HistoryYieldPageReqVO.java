package com.yutoudev.irrigation.irr.controller.admin.history.vo.yield;

import lombok.*;

import java.time.*;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryYieldDO;

/**
 *
 * 历史出库水量分页RequestVO
 * @description 管理后台-历史出库水量分页RequestVO
 * <AUTHOR>
 * @time 2024-12-01 21:37:49
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HistoryYieldPageReqVO extends PageCriteria<HistoryYieldDO> {

    /**
     * 名称
     * 
     */
    private String name;

    /**
     * 编号
     * 
     */
    private Long code;

    /**
     * 统计频率
     * @mock 1年 2月
     */
    private Integer frequency;

    /**
     * 水源地ID
     * 
     */
    private Long swhsId;

    /**
     * 水源地名称
     * 
     */
    private String swhsName;

    /**
     * 合计值
     * 
     */
    private Double total;

    /**
     * 最大出库水量日期
     * 
     */
    private LocalDate maxDay;

    /**
     * 最大出库水量
     * 
     */
    private Double maxVolume;

    /**
     * 最小出库水量日期
     * 
     */
    private LocalDate minDay;

    /**
     * 最小出库水量
     * 
     */
    private Double minVolume;

    /**
     * 平均出库水量
     * 
     */
    private Double avgVolume;

    /**
     * 制表人
     * 
     */
    private String lister;

    /**
     * 制表时间
     * 
     */
    private LocalDateTime markTime;

    /**
     * 备注
     * 
     */
    private String remark;

}

package com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindmonth;

import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndMonthDO;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 *
 * 量测设备指标统计月分页RequestVO
 * @description 管理后台-量测设备指标统计月分页RequestVO
 * <AUTHOR>
 * @time 2024-06-16 20:28:50
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeasIndMonthPageReqVO extends PageCriteria<MeasIndMonthDO> {

    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 站点ID
     *
     */
    private Long equipId;

    /**
     * 中心站ID
     * 
     */
    private String centralId;

    /**
     * 遥测站ID
     * 
     */
    private String devId;

    /**
     * 年月
     * 
     */
    private String month;

    /**
     * 日降水量累计值
     *
     */
    private Float rainfallVolume;

    /**
     * 更新时间
     *
     */
    private LocalDateTime updateTime;

}

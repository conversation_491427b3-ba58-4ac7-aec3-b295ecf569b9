package com.yutoudev.irrigation.irr.controller.admin.rainfallprocess.vo.site;

import com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess.RainfallProcessSiteDO;
import io.github.portaldalaran.taming.pojo.QueryCriteria;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 雨量站降雨过程list查询RequestVO
 *
 * <AUTHOR>
 */
@Data
public class RainfallProcessSiteQueryReqVO extends QueryCriteria<RainfallProcessSiteDO> {

    /**
     * ID
     */
    private Long id;

    /**
     * 代码
     */
    private Long code;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 累计降雨量
     */
    private Double rainfall;

    /**
     * 开始降雨时间
     */
    private LocalDateTime startTime;

    /**
     * 结束降雨时间
     */
    private LocalDateTime endTime;

    /**
     * 入库流量开始变大时间
     */
    private LocalDateTime largeTime;

    /**
     * 入库流量开始变小时间
     */
    private LocalDateTime smallTime;

    /**
     * 径流入库时间
     *
     * @mock 开始降雨时间~入库流量开始变大之间的时间
     */
    private Integer runoffArrivalTime;

    /**
     * 径流时间
     *
     * @mock 入库流量开始变大~入库流量开始变小之间的时间
     */
    private Integer runoffTime;

    /**
     * 消退系数
     *
     * @mock 1-Em/Wm
     */
    private Double fadingCoef;

    /**
     * 水源地ID
     */
    private Long swhsId;

    /**
     * 水源地名称
     */
    private String swhsName;

    /**
     * 设备ID
     */
    private Long equipId;

    /**
     * 设备名称
     */
    private String equipName;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;

}

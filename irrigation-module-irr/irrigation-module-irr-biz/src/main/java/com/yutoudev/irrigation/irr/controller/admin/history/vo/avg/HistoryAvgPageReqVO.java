package com.yutoudev.irrigation.irr.controller.admin.history.vo.avg;

import lombok.*;
import io.github.portaldalaran.taming.pojo.PageCriteria;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryAvgDO;

/**
 *
 * 历史月均值分页RequestVO
 * @description 管理后台-历史月均值分页RequestVO
 * <AUTHOR>
 * @time 2024-11-29 21:29:03
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HistoryAvgPageReqVO extends PageCriteria<HistoryAvgDO> {

    /**
     * 水源地ID
     * 
     */
    private Long swhsId;

    /**
     * 水源地名称
     * 
     */
    private String swhsName;

    /**
     * 类型
     * @mock 1降雨量 2来水量
     */
    private Integer type;

    /**
     * 合计值
     * 
     */
    private Double total;

    /**
     * 1月均值
     * 
     */
    private Double m1;

    /**
     * 2月均值
     * 
     */
    private Double m2;

    /**
     * 3月均值
     * 
     */
    private Double m3;

    /**
     * 4月均值
     * 
     */
    private Double m4;

    /**
     * 5月均值
     * 
     */
    private Double m5;

    /**
     * 6月均值
     * 
     */
    private Double m6;

    /**
     * 7月均值
     * 
     */
    private Double m7;

    /**
     * 8月均值
     * 
     */
    private Double m8;

    /**
     * 9月均值
     * 
     */
    private Double m9;

    /**
     * 10月均值
     * 
     */
    private Double m10;

    /**
     * 11月均值
     * 
     */
    private Double m11;

    /**
     * 12月均值
     * 
     */
    private Double m12;

}

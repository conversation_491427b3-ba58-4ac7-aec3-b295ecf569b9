package com.yutoudev.irrigation.irr.controller.admin.flood.vo.routingreservoir;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 水库调洪演算 Base VO
 *
 * <AUTHOR>
 */
@Data
public class RoutingReservoirBaseVO {

    /**
     * 次数
     */
    @NotNull(message = "次数不能为空")
    private Integer code;

    /**
     * 年度
     */
    private Integer year;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 数据频率
     */
    private Integer deltaTime;

    /**
     * 库容曲线取值算法
     *
     * @mock 1:插值法 2:查表法
     */
    private Integer storageInterpolate;

    /**
     * 泄洪曲线取值算法
     *
     * @mock 1:插值法 2:查表法
     */
    private Integer dischargeInterpolate;

    /**
     * 下游允许最大下泄流量
     */
    private Double maxAllowedDischarge;

    /**
     * 起调水位
     */
    private Double initWaterLevel;

    /**
     * 汛期防洪限制水位(m)
     */
    private Double controlLevel;

    /**
     * 控制下泄流量
     */
    private Double controlDischarge;

    /**
     * 计算单位
     */
    private Integer volumeUnit;

    /**
     * 产流过程ID
     */
    private Long processId;

    /**
     * 水源ID
     */
    private Long swhsId;

    /**
     * 水源名称
     */
    private String swhsName;

    /**
     * 是否已经计算
     *
     * @mock 0初始 1已经计算
     */
    @NotNull(message = "是否已经计算不能为空")
    private Integer status;

    /**
     * 最大下泄流量
     */
    private Double maxOutFlow;

    /**
     * 最大下泄时间
     */
    private LocalDateTime maxOutFlowTime;

    /**
     * 最高洪水位
     */
    private Double maxWaterLevel;

    /**
     * 最高洪水时间
     */
    private LocalDateTime maxWaterLevelTime;

    /**
     * 最大库容
     */
    private Double maxStorage;

    /**
     * 最大库容时间
     */
    private LocalDateTime maxStorageTime;

    /**
     * 下泄流量过程线
     */
    private String outflowHydrograph;

    /**
     * 库容变化过程
     */
    private String storageVolumes;

    /**
     * 水库水位变化过程
     */
    private String waterLevels;
}

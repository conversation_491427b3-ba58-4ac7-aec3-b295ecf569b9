package com.yutoudev.irrigation.irr.controller.admin.coef.vo.levelflow;

import lombok.*;

/**
 *
 * 水位流量关系表DetailResponseVO
 * @description 管理后台-水位流量关系表DetailResponseVO
 * <AUTHOR>
 * @time 2024-08-08 20:51:24
 *
 */
@Data
@ToString(callSuper = true)
public class CoefLevelFlowDetailRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 设备ID
     * 
     */
    private Long equipId;

    /**
     * 水位
     * 
     */
    private Double waterLevel;

    /**
     * 系数
     * 
     */
    private Double coef;
}

package com.yutoudev.irrigation.irr.convert.history;

import java.util.*;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;

import com.yutoudev.irrigation.irr.controller.admin.history.vo.yieldday.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yutoudev.irrigation.irr.dal.dataobject.history.HistoryYieldDayDO;

/**
 *
 * 历史每日出库水量Convert
 * @description 管理后台-历史每日出库水量 mapstruct Convert
 * <AUTHOR>
 * @time 2024-12-01 21:37:50
 *
 */
@Mapper
public interface HistoryYieldDayConvert {

    HistoryYieldDayConvert INSTANCE = Mappers.getMapper(HistoryYieldDayConvert.class);

    /**
    * CreateReqVO转为DO
    * @param createReqVO createReqVO
    * @return DO
    */
    HistoryYieldDayDO convert(HistoryYieldDayCreateReqVO createReqVO);

    /**
    * 批量把 CreateReqVO 转为 DO
    * @param list CreateReqVO list
    * @return DO list
    */
    List<HistoryYieldDayDO> convertCreateBatch(List<HistoryYieldDayCreateReqVO> list);

    /**
    * UpdateReqVO转为DO
    * @param updateReqVO updateReqVO
    * @return DO
    */
    HistoryYieldDayDO convert(HistoryYieldDayUpdateReqVO updateReqVO);

    /**
    * 批量把 UpdateReqVO转为DO
    * @param list UpdateReqVO list
    * @return DO
    */
    List<HistoryYieldDayDO> convertUpdateBatch(List<HistoryYieldDayUpdateReqVO> list);

    /**
    * DO转化为RespVO
    * @param bean DO
    * @return RespVO
    */
    HistoryYieldDayRespVO convert(HistoryYieldDayDO bean);

    /**
    * DO转化为 DetailRespVO
    * @param bean DO
    * @return DetailRespVO
    */
    HistoryYieldDayDetailRespVO convertDetail(HistoryYieldDayDO bean);

    /**
    * 批量把DO转化为RespVO
    * @param list DO list
    * @return RespVO list
    */
    List<HistoryYieldDayRespVO> convertList(List<HistoryYieldDayDO> list);

    /**
    * 把PageResult中的DO转化为RespVO
    * @param page PageResult DO
    * @return PageResult RespVO
    */
    PageResult<HistoryYieldDayRespVO> convertPage(PageResult<HistoryYieldDayDO> page);

    /**
    * 批量把DO转化为ExcelVO
    * @param list DO list
    * @return ExcelVO list
    */
    List<HistoryYieldDayExcelVO> convertExportExcel(List<HistoryYieldDayDO> list);

    /**
    * 批量把ExcelVO转化为DO
    * @param list ExcelVO list
    * @return DO list
    */
    List<HistoryYieldDayDO> convertImportExcel(List<HistoryYieldDayExcelVO> list);
}

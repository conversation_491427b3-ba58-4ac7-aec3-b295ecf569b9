package com.yutoudev.irrigation.irr.job.measanalysisday;

import com.yutoudev.irrigation.framework.quartz.core.handler.JobHandler;
import com.yutoudev.irrigation.framework.tenant.core.job.TenantJob;
import com.yutoudev.irrigation.irr.dal.dataobject.measanalysisday.MeasAnalysisDayDO;
import com.yutoudev.irrigation.irr.service.measanalysisday.MeasAnalysisDayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 设备拍照定时任务
 *
 * <AUTHOR>
 */
@Component
@TenantJob
@Slf4j
public class CalcCatchmentRainfallAvgJob implements JobHandler {

    @Resource
    private MeasAnalysisDayService<MeasAnalysisDayDO> measAnalysisDayService;

    @Override
    public String execute(String param) {
        log.info("开始执行集水区平均降量计算");
        return measAnalysisDayService.calCatchmentRainfallAvg(Integer.parseInt(param));
    }
}

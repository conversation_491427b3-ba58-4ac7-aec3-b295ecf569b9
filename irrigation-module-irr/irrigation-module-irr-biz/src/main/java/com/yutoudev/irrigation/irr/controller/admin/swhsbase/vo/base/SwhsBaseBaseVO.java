package com.yutoudev.irrigation.irr.controller.admin.swhsbase.vo.base;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;

/**
 * 水源基础信息 Base VO
 *
 * <AUTHOR>
 */
@Data
public class SwhsBaseBaseVO {

    /**
     * 水源代码
     */
    private String swhsCode;

    /**
     * 水源名称
     */
    @NotNull(message = "水源名称不能为空")
    @Length(max = 100, message = "水源名称长度不能超过100")
    private String swhsName;

    /**
     * 水源基点经度
     */
    private Double swhsBaseLng;

    /**
     * 水源基点纬度
     */
    private Double swhsBaseLat;

    /**
     * 水源所在位置
     */
    private String swhsLoc;

    /**
     * 水源基点GIS
     */
    private String swhsBaseLocGis;

    /**
     * 描边颜色
     */
    private String color;

    /**
     * 填充颜色
     */
    private String styleFillColor;

    /**
     * 填充不透明度
     */
    private Double styleFillOpacity;

    /**
     * 笔画宽度
     */
    private Integer styleWidth;

    /**
     * 所在流域
     */
    private String basin;

    /**
     * 管理单位
     */
    private String management;

    /**
     * 主管单位
     */
    private String sponsor;

    /**
     * 工程等级
     * 枚举
     */
    private Integer engineeringGrade;

    /**
     * 允许最大降雨量
     */
    private String allowMaxRainfall;

    /**
     * 历史最低水位
     */
    private Double lowestWaterLevel;

    /**
     * 历史最低流量
     */
    private Double lowestTraffic;

    /**
     * 水电站水头
     */
    private Double powerHead;

    /**
     * 水电站效率（%）
     */
    private Double powerEfficiency;

    /**
     * 年发电量（KWh）
     */
    private Double powerGeneration;

    /**
     * 装置容量（KW）
     */
    private Double deviceCapacity;

    /**
     * 主要取水用途
     */
    private Integer mainWainUse;

    /**
     * 设计年取水量
     */
    private Double desAnnWain;

    /**
     * 取水水源类型
     */
    private Integer wainWasoType;

    /**
     * 水面面积
     */
    private Double waterArea;

    /**
     * 集雨面积
     */
    private Double catchmentArea;

    /**
     * 取水口数量
     */
    private Integer wainNum;

    /**
     * 防洪限制水位
     */
    private Double floodLimitLevel;

    /**
     * 防洪高水位
     */
    private Double floodUpperLevel;

    /**
     * 防洪限制库容
     */
    private Double floodLimitCapacity;

    /**
     * 调洪库容
     */
    private Double storageFloodCapacity;

    /**
     * 防洪库容
     */
    private Double floodCapacity;

    /**
     * 设计洪水位
     */
    private Double floodDesignLevel;

    /**
     * 校核洪水位
     */
    private Double floodCheckLevel;

    /**
     * 4-7月汛限水位
     */
    private Double floodM4Level;

    /**
     * 8-9月汛限水位
     */
    private Double floodM8Level;

    /**
     * 死水位
     */
    private Double deadWaterLevel;

    /**
     * 死库容
     */
    private Double deadCapacity;

    /**
     * 运行控制水位
     */
    private Double controlWaterLevel;

    /**
     * 调节库容
     */
    private Double storageRegulatingCapacity;

    /**
     * 兴利库容
     */
    private Double utilizableCapacity;

    /**
     * 总库容
     */
    private Double totalCapacity;

    /**
     * 正常水位
     */
    private Double normalWaterLevel;

    /**
     * 正常水位相应水面面积
     */
    private Double normalWaterArea;

    /**
     * 正常水位相应库容
     */
    private Double normalWaterCapacity;

    /**
     * 算法模型
     */
    private Long algorithmModel;

    /**
     * 最大预警值
     */
    private Double maxWarningValue;

    /**
     * 生态基流水口ID
     */
    private Long baseFlowGateId;

    /**
     * 平均径流入库时间(小时)
     */
    private Integer runoffArrivalTime;

    /**
     * 平均径流时间(小时)
     */
    private Integer runoffTime;

    /**
     * 备注
     */
    private String note;
}

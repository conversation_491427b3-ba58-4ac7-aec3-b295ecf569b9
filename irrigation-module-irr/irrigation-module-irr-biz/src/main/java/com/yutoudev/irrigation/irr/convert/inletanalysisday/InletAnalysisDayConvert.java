package com.yutoudev.irrigation.irr.convert.inletanalysisday;

import java.util.*;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yutoudev.irrigation.irr.controller.admin.inletanalysisday.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.inletanalysisday.InletAnalysisDayDO;

/**
 *
 * 日来水分析Convert
 * @description 管理后台-日来水分析 mapstruct Convert
 * <AUTHOR>
 * @time 2024-07-23 14:33:36
 *
 */
@Mapper
public interface InletAnalysisDayConvert {

    InletAnalysisDayConvert INSTANCE = Mappers.getMapper(InletAnalysisDayConvert.class);

    /**
    * CreateReqVO转为DO
    * @param createReqVO createReqVO
    * @return DO
    */
    InletAnalysisDayDO convert(InletAnalysisDayCreateReqVO createReqVO);

    /**
    * 批量把 CreateReqVO 转为 DO
    * @param list CreateReqVO list
    * @return DO list
    */
    List<InletAnalysisDayDO> convertCreateBatch(List<InletAnalysisDayCreateReqVO> list);

    /**
    * UpdateReqVO转为DO
    * @param updateReqVO updateReqVO
    * @return DO
    */
    InletAnalysisDayDO convert(InletAnalysisDayUpdateReqVO updateReqVO);

    /**
    * 批量把 UpdateReqVO转为DO
    * @param list UpdateReqVO list
    * @return DO
    */
    List<InletAnalysisDayDO> convertUpdateBatch(List<InletAnalysisDayUpdateReqVO> list);

    /**
    * DO转化为RespVO
    * @param bean DO
    * @return RespVO
    */
    InletAnalysisDayRespVO convert(InletAnalysisDayDO bean);

    /**
    * DO转化为 DetailRespVO
    * @param bean DO
    * @return DetailRespVO
    */
    InletAnalysisDayDetailRespVO convertDetail(InletAnalysisDayDO bean);

    /**
    * 批量把DO转化为RespVO
    * @param list DO list
    * @return RespVO list
    */
    List<InletAnalysisDayRespVO> convertList(List<InletAnalysisDayDO> list);

    /**
    * 把PageResult中的DO转化为RespVO
    * @param page PageResult DO
    * @return PageResult RespVO
    */
    PageResult<InletAnalysisDayRespVO> convertPage(PageResult<InletAnalysisDayDO> page);

    /**
    * 批量把DO转化为ExcelVO
    * @param list DO list
    * @return ExcelVO list
    */
    List<InletAnalysisDayExcelVO> convertExportExcel(List<InletAnalysisDayDO> list);

    /**
    * 批量把ExcelVO转化为DO
    * @param list ExcelVO list
    * @return DO list
    */
    List<InletAnalysisDayDO> convertImportExcel(List<InletAnalysisDayExcelVO> list);
}

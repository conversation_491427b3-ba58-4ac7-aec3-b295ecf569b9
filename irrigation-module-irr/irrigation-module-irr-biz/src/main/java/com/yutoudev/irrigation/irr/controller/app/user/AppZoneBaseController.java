package com.yutoudev.irrigation.irr.controller.app.user;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseDetailRespVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBasePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseRespVO;
import com.yutoudev.irrigation.irr.convert.zonebase.ZoneBaseConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.zonebase.ZoneBaseDO;
import com.yutoudev.irrigation.irr.service.zonebase.ZoneBaseService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;


/**
 * APP - 用户片区信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/zone-base")
@Validated
public class AppZoneBaseController {

    private static final String MODULE_NAME = "片区";

    @Resource
    private ZoneBaseService<ZoneBaseDO> zoneBaseService;


    /**
     * 获得片区详情
     *
     * @param id 编号 Long
     * @return CommonResult<ZoneBaseDetailRespVO> 详情响应VO
     * @description 根据ID取对象所有字段
     */
    @GetMapping("/get")
    public CommonResult<ZoneBaseDetailRespVO> get(@RequestParam("id") Long id) {
        ZoneBaseDO zoneBase = zoneBaseService.get(id);
        return success(ZoneBaseConvert.INSTANCE.convertDetail(zoneBase));
    }

    /**
     * 片区列表
     *
     * @param queryReqVO 查询条件 ZoneBaseQueryReqVO
     * @return CommonResult<List < ZoneBaseRespVO>> 列表响应VO
     * @description 根据查询条件筛选列表值，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/list")
    public CommonResult<List<ZoneBaseRespVO>> getList(@RequestQueryParam ZoneBaseQueryReqVO queryReqVO) {
        List<ZoneBaseDO> list = zoneBaseService.getList(queryReqVO);
        return success(ZoneBaseConvert.INSTANCE.convertList(list));
    }

    /**
     * 片区分页
     *
     * @param pageVO 查询条件 ZoneBasePageReqVO
     * @return CommonResult<PageResult < ZoneBaseRespVO>> 列表响应VO
     * @description 根据查询条件分布查询，查询条件以RequestVO为基准，可以使用talons查询组装器扩展
     */
    @GetMapping("/page")
    public CommonResult<PageResult<ZoneBaseRespVO>> page(@RequestQueryParam ZoneBasePageReqVO pageVO) {
        PageResult<ZoneBaseDO> pageResult = zoneBaseService.page(pageVO);
        return success(ZoneBaseConvert.INSTANCE.convertPage(pageResult));
    }
}
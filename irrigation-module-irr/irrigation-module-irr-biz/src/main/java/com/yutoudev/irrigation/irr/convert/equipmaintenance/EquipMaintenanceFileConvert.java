package com.yutoudev.irrigation.irr.convert.equipmaintenance;

import java.util.*;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yutoudev.irrigation.irr.controller.admin.equipmaintenancefile.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.equipmaintenance.EquipMaintenanceFileDO;

/**
 *
 * 设备维护附件Convert
 * @description 管理后台-设备维护附件 mapstruct Convert
 * <AUTHOR>
 * @time 2024-10-27 10:33:14
 *
 */
@Mapper
public interface EquipMaintenanceFileConvert {

    EquipMaintenanceFileConvert INSTANCE = Mappers.getMapper(EquipMaintenanceFileConvert.class);

    /**
    * CreateReqVO转为DO
    * @param createReqVO createReqVO
    * @return DO
    */
    EquipMaintenanceFileDO convert(EquipMaintenanceFileCreateReqVO createReqVO);

    /**
    * 批量把 CreateReqVO 转为 DO
    * @param list CreateReqVO list
    * @return DO list
    */
    List<EquipMaintenanceFileDO> convertCreateBatch(List<EquipMaintenanceFileCreateReqVO> list);

    /**
    * UpdateReqVO转为DO
    * @param updateReqVO updateReqVO
    * @return DO
    */
    EquipMaintenanceFileDO convert(EquipMaintenanceFileUpdateReqVO updateReqVO);

    /**
    * 批量把 UpdateReqVO转为DO
    * @param list UpdateReqVO list
    * @return DO
    */
    List<EquipMaintenanceFileDO> convertUpdateBatch(List<EquipMaintenanceFileUpdateReqVO> list);

    /**
    * DO转化为RespVO
    * @param bean DO
    * @return RespVO
    */
    EquipMaintenanceFileRespVO convert(EquipMaintenanceFileDO bean);

    /**
    * DO转化为 DetailRespVO
    * @param bean DO
    * @return DetailRespVO
    */
    EquipMaintenanceFileDetailRespVO convertDetail(EquipMaintenanceFileDO bean);

    /**
    * 批量把DO转化为RespVO
    * @param list DO list
    * @return RespVO list
    */
    List<EquipMaintenanceFileRespVO> convertList(List<EquipMaintenanceFileDO> list);

    /**
    * 把PageResult中的DO转化为RespVO
    * @param page PageResult DO
    * @return PageResult RespVO
    */
    PageResult<EquipMaintenanceFileRespVO> convertPage(PageResult<EquipMaintenanceFileDO> page);

    /**
    * 批量把DO转化为ExcelVO
    * @param list DO list
    * @return ExcelVO list
    */
    List<EquipMaintenanceFileExcelVO> convertExportExcel(List<EquipMaintenanceFileDO> list);

    /**
    * 批量把ExcelVO转化为DO
    * @param list ExcelVO list
    * @return DO list
    */
    List<EquipMaintenanceFileDO> convertImportExcel(List<EquipMaintenanceFileExcelVO> list);
}

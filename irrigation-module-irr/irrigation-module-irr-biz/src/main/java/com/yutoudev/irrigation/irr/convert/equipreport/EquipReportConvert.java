package com.yutoudev.irrigation.irr.convert.equipreport;

import com.yutoudev.irrigation.framework.common.mapstruct.TypeConversion;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.irr.controller.admin.equipreport.vo.*;
import com.yutoudev.irrigation.irr.dal.dataobject.equipreport.EquipReportDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 * 设备数据上报Convert
 * @description 管理后台-设备数据上报 mapstruct Convert
 * <AUTHOR>
 * @time 2024-05-12 21:03:33
 *
 */
@Mapper(uses = TypeConversion.class)
public interface EquipReportConvert {

    EquipReportConvert INSTANCE = Mappers.getMapper(EquipReportConvert.class);

    /**
    * CreateReqVO转为DO
    * @param createReqVO createReqVO
    * @return DO
    */
    EquipReportDO convert(EquipReportCreateReqVO createReqVO);

    /**
    * 批量把 CreateReqVO 转为 DO
    * @param list CreateReqVO list
    * @return DO list
    */
    List<EquipReportDO> convertCreateBatch(List<EquipReportCreateReqVO> list);

    /**
    * UpdateReqVO转为DO
    * @param updateReqVO updateReqVO
    * @return DO
    */
    EquipReportDO convert(EquipReportUpdateReqVO updateReqVO);

    /**
    * 批量把 UpdateReqVO转为DO
    * @param list UpdateReqVO list
    * @return DO
    */
    List<EquipReportDO> convertUpdateBatch(List<EquipReportUpdateReqVO> list);

    /**
    * DO转化为RespVO
    * @param bean DO
    * @return RespVO
    */
    EquipReportRespVO convert(EquipReportDO bean);

    @Mappings({
            @Mapping(source = "reportTime", target = "reportTime", qualifiedByName = "convertLocalDateTimeToDate"),
    })
    EquipReportExcelVO convertReportExcelVO(EquipReportDO bean);

    /**
    * DO转化为 DetailRespVO
    * @param bean DO
    * @return DetailRespVO
    */
    EquipReportDetailRespVO convertDetail(EquipReportDO bean);

    /**
    * 批量把DO转化为RespVO
    * @param list DO list
    * @return RespVO list
    */
    List<EquipReportRespVO> convertList(List<EquipReportDO> list);

    /**
    * 把PageResult中的DO转化为RespVO
    * @param page PageResult DO
    * @return PageResult RespVO
    */
    PageResult<EquipReportRespVO> convertPage(PageResult<EquipReportDO> page);

    /**
    * 批量把DO转化为ExcelVO
    * @param list DO list
    * @return ExcelVO list
    */
    List<EquipReportExcelVO> convertExportExcel(List<EquipReportDO> list);

    /**
    * 批量把ExcelVO转化为DO
    * @param list ExcelVO list
    * @return DO list
    */
    List<EquipReportDO> convertImportExcel(List<EquipReportExcelVO> list);
}

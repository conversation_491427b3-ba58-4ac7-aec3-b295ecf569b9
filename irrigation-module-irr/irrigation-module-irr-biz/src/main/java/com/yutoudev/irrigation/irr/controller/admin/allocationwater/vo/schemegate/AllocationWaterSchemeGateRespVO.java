package com.yutoudev.irrigation.irr.controller.admin.allocationwater.vo.schemegate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseRespVO;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 *
 * 配水方案闸门配置ResponseVO
 * @description 管理后台-配水方案闸门配置ResponseVO
 * <AUTHOR>
 * @time 2024-11-27 21:47:09
 *
 */
@Data
@ToString(callSuper = true)
public class AllocationWaterSchemeGateRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 配水管理ID
     * 
     */
    private Long allocationWaterId;

    /**
     * 配水方案ID
     * 
     */
    private Long allocationWaterSchemeId;

    /**
     * 水口ID
     * 
     */
    private Long gateId;

    /**
     * 站点ID
     */
    private Long equipId;

    /**
     * 站点明细
     */
    private EquipBaseRespVO equip;

    /**
     * 配水量
     * 
     */
    private Double water;

    /**
     * 配水流量
     * 
     */
    private Double flowRate;

    /**
     * 设备开闸高度
     * 
     */
    private Double gateHeight;

    /**
     * 闸控时间
     * 
     */
    private LocalDateTime gateTime;

    /**
     * 创建时间
     * 
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date createTime;

    /**
     * 修改时间
     * 
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Date updateTime;
}
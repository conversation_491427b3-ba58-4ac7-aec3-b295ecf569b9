package com.yutoudev.irrigation.irr.dal.dataobject.waterflowconfig;

import lombok.*;
import java.util.*;
import java.time.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;

/**
 *
 * 水流配置DO
 * @description 管理后台-水流配置数据库对象
 * <AUTHOR>
 * @time 2024-08-13 15:15:33
 *
 */
@TableName(value = "irr_water_flow_config", autoResultMap = true)
@KeySequence("irr_water_flow_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WaterFlowConfigDO extends BaseDO {

    /**
     * ID
     * 
     */
    @TableId
    private Long id;

    /**
     * 名称
     * 
     */
    private String name;

    /**
     * 备注
     * 
     */
    private String note;

    @TableField(exist = false)
    private Boolean deleted;
}

package com.yutoudev.irrigation.irr.controller.admin.measrealtime.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

import static com.yutoudev.irrigation.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 量测设备实时数据DetailResponseVO
 *
 * <AUTHOR>
 * @description 管理后台-量测设备实时数据DetailResponseVO
 * @time 2024-05-20 15:01:16
 */
@Data
@ToString(callSuper = true)
public class MeasRealtimeDetailRespVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 设备ID
     */
    private Long equipId;

    /**
     * 中心站ID
     */
    private String centralId;

    /**
     * 遥测站ID
     */
    private String devId;

    /**
     * 观测时间引导符
     */
    private Float v1;

    /**
     * 测站编码引导符
     */
    private Float v2;

    /**
     * 1小时内每5分钟时段雨量
     */
    private Float v3;

    /**
     * 1小时内每5分钟时段水位1
     */
    private Float v4;

    /**
     * 1小时内每5分钟时段水位2
     */
    private Float v5;

    /**
     * 闸坝、水库闸门开启高度
     */
    private Float v6;

    /**
     * 日降水量
     */
    private Float v7;

    /**
     * 当前降水量
     */
    private Float v8;

    /**
     * 降水量累计值
     */
    private Float v9;

    /**
     * 瞬时流量
     */
    private Float v10;

    /**
     * 总出库流量、过闸总流量
     */
    private Float v11;

    /**
     * 断面平均流速
     */
    private Float v12;

    /**
     * 当前瞬时流速
     */
    private Float v13;

    /**
     * 电源电压
     */
    private Float v14;

    /**
     * 瞬时河道水位
     */
    private Float v15;

    /**
     * 交流A相电压
     */
    private Float v16;

    /**
     * 交流B相电压
     */
    private Float v17;

    /**
     * 交流C相电压
     */
    private Float v18;

    /**
     * 交流A相电流
     */
    private Float v19;

    /**
     * 交流B相电流
     */
    private Float v20;

    /**
     * 交流C相电流
     */
    private Float v21;

    /**
     * 日蒸发量
     */
    private Float v22;

    /**
     * 当前蒸发量（毫米）
     */
    private Float v23;

    /**
     * 风向
     */
    private String v24;

    /**
     * 风速
     */
    private Float v25;

    /**
     * 瞬时气温
     */
    private Float v26;

    /**
     * 湿度
     */
    private Float v27;

    /**
     * 气压
     */
    private Float v28;

    /**
     * PH值
     */
    private Float v29;

    /**
     * 溶解氧
     */
    private Float v30;

    /**
     * 电导率
     */
    private Float v31;

    /**
     * 浊度
     */
    private Float v32;

    /**
     * 高锰酸盐指数
     */
    private Float v33;

    /**
     * 氨氮
     */
    private Float v34;

    /**
     * 应变
     */
    private Float v35;

    /**
     * 渗流
     */
    private Float v36;

    /**
     * 渗压
     */
    private Float v37;

    /**
     * 沉降
     */
    private Float v38;

    /**
     * 水库水位
     */
    private Float v39;

    /**
     * 5分钟降水量
     */
    private Float v40;

    /**
     * 闸坝、水库闸门开启高度
     */
    private Float v41;

    /**
     * 预留
     */
    private Float v42;

    /**
     * 报警信息
     */
    private String v43;

    /**
     * 用水量
     */
    private Float v44;

    /**
     * 1小时降雨
     */
    private Float v45;

    /**
     * 小时累计降水量
     */
    private Float v46;

    /**
     * 实时降水量
     *
     */
    private Float rainfall;

    /**
     * 日累计降水量
     */
    private Float dayRainfall;

    /**
     * 月累计降水量
     */
    private Float monthRainfall;

    /**
     * 更新时间
     *
     */
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updateTime;
}

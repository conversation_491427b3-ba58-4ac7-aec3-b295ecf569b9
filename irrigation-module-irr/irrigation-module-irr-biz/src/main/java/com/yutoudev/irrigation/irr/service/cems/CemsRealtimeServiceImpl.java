package com.yutoudev.irrigation.irr.service.cems;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.irr.config.cems.CemsProperties;
import com.yutoudev.irrigation.irr.dal.dataobject.cems.CemsRealtimeDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.mysql.cems.CemsRealtimeMapper;
import com.yutoudev.irrigation.irr.dal.mysql.equipbase.EquipBaseMapper;
import com.yutoudev.irrigation.irr.enums.EquipOnlineStatusEnum;
import com.yutoudev.irrigation.irr.token.CemsUtil;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Validated
public class CemsRealtimeServiceImpl extends TalonsServiceImpl<CemsRealtimeMapper, CemsRealtimeDO> implements CemsRealtimeService<CemsRealtimeDO> {

    @Resource
    private EquipBaseMapper equipBaseMapper;

    @Resource
    private CemsProperties cemsProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CemsRealtimeDO update(Long quipId) {
        CemsRealtimeDO cemsRealtimeDO = null;
        EquipBaseDO equip = equipBaseMapper.selectById(quipId);
        JSONObject data = CemsUtil.getData(cemsProperties.getUrl(), equip.getStCode());
        if (data != null  && data.getJSONArray("digital") !=null) {
            JSONArray digital = data.getJSONArray("digital");
            cemsRealtimeDO = new CemsRealtimeDO();
            cemsRealtimeDO.setDevId(equip.getDevId());
            cemsRealtimeDO.setCentralId(equip.getCentralId());
            cemsRealtimeDO.setEquipId(equip.getId());
            cemsRealtimeDO.setDigital1(digital.getFloat(0));
            cemsRealtimeDO.setDigital2(digital.getFloat(1));
            cemsRealtimeDO.setDigital3(digital.getFloat(2));
            cemsRealtimeDO.setDigital4(digital.getFloat(3));
            cemsRealtimeDO.setDigital5(digital.getFloat(4));
            cemsRealtimeDO.setDigital6(digital.getFloat(5));
            cemsRealtimeDO.setDigital7(digital.getFloat(6));
            cemsRealtimeDO.setDigital8(digital.getFloat(7));
            cemsRealtimeDO.setDigital9(digital.getFloat(8));
            cemsRealtimeDO.setDigital10(digital.getFloat(9));
            cemsRealtimeDO.setDigital11(digital.getFloat(10));
            cemsRealtimeDO.setUpdateTime(LocalDateTime.now());

            LambdaQueryWrapperX<CemsRealtimeDO> cemsRealtimeDOWrapper = new LambdaQueryWrapperX<>();
            cemsRealtimeDOWrapper.eq(CemsRealtimeDO::getEquipId, equip.getId());
            List<CemsRealtimeDO> cemsRealtimes = baseMapper.selectList(cemsRealtimeDOWrapper);
            if (cemsRealtimes.size() > 0) {
                cemsRealtimeDO.setId(cemsRealtimes.get(0).getId());
                baseMapper.updateById(cemsRealtimeDO);
            } else {
                baseMapper.insert(cemsRealtimeDO);
            }

            // 更新在线状态
            equipBaseMapper.updateById(EquipBaseDO.builder().id(equip.getId()).online(EquipOnlineStatusEnum.ONLINE.getStatus()).build());
        } else {
            equipBaseMapper.updateById(EquipBaseDO.builder().id(equip.getId()).online(EquipOnlineStatusEnum.OFFLINE.getStatus()).build());
        }
        return cemsRealtimeDO;
    }
}

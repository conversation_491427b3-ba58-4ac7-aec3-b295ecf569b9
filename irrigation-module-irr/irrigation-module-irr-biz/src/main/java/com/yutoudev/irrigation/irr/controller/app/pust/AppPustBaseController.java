package com.yutoudev.irrigation.irr.controller.app.pust;

import com.yutoudev.irrigation.framework.common.pojo.CommonResult;
import com.yutoudev.irrigation.framework.operatelog.core.annotations.OperateLog;
import com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeConstants;
import com.yutoudev.irrigation.irr.controller.admin.pustbase.vo.PustBaseQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.pustbase.vo.PustBaseSimpleRespVO;
import com.yutoudev.irrigation.irr.convert.pustbase.PustBaseConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.pustbase.PustBaseDO;
import com.yutoudev.irrigation.irr.service.pustbase.PustBaseService;
import io.github.portaldalaran.taming.annotation.RequestQueryParam;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yutoudev.irrigation.framework.common.pojo.CommonResult.success;
import static com.yutoudev.irrigation.framework.operatelog.core.enums.OperateTypeEnum.GET;


/**
 * 泵站基础信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irr/pust-base")
@Validated
public class AppPustBaseController {

    private static final String MODULE_NAME = "泵站基础信息";

    @Resource
    private PustBaseService<PustBaseDO> pustBaseService;

    /**
     * 泵站基础信息列表
     *
     * @param queryReqVO 查询条件 PustBaseQueryReqVO
     * @return CommonResult<List < PustBaseSimpleRespVO>> 列表响应VO
     */
    @GetMapping("/list-simple")
    @OperateLog(module = MODULE_NAME, name = OperateTypeConstants.LIST, type = GET)
    public CommonResult<List<PustBaseSimpleRespVO>> getSimpleList(@RequestQueryParam PustBaseQueryReqVO queryReqVO) {
        List<PustBaseDO> list = pustBaseService.getList(queryReqVO);
        return success(PustBaseConvert.INSTANCE.convertSimpleList(list));
    }

}
package com.yutoudev.irrigation.irr.service.waterfee;

import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.mybatis.core.dataobject.BaseDO;
import com.yutoudev.irrigation.irr.controller.admin.waterfee.vo.waterbillitem.*;
import com.yutoudev.irrigation.irr.dal.dataobject.waterfee.WaterBillItemDO;
import io.github.portaldalaran.talons.core.ITalonsService;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 用水账单明细Service接口
 *
 * <AUTHOR>
 * @description 管理后台-用水账单明细Service接口，对ITalonsService的扩展
 * @time 2024-08-25 23:07:18
 */
public interface WaterBillItemService<T extends BaseDO> extends ITalonsService<T> {

    /**
     * 创建用水账单明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long create(@Valid WaterBillItemCreateReqVO createReqVO);

    /**
     * 批量创建用水账单明细
     *
     * @param list 创建信息
     * @return 编号
     */
    boolean createBatch(@Valid List<WaterBillItemCreateReqVO> list);

    /**
     * 更新用水账单明细
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid WaterBillItemUpdateReqVO updateReqVO);

    /**
     * 批量更新用水账单明细
     *
     * @param list 更新信息
     */
    boolean updateBatch(@Valid List<WaterBillItemUpdateReqVO> list);

    /**
     * 删除用水账单明细
     *
     * @param id 编号
     */
    void delete(Long id);

    /**
     * 批量删除用水账单明细
     *
     * @param ids 编号
     */
    boolean deleteBatch(List<Long> ids);

    /**
     * 获得用水账单明细
     *
     * @param id 编号
     * @return 用水账单明细
     */
    WaterBillItemDO get(Long id);

    /**
     * 用水账单明细列表
     *
     * @param ids 编号
     * @return 用水账单明细列表
     */
    List<WaterBillItemDO> getList(List<Long> ids);

    /**
     * 用水账单明细分页
     *
     * @param pageReqVO 分页查询
     * @return 用水账单明细分页
     */
    PageResult<WaterBillItemDO> page(WaterBillItemPageReqVO pageReqVO);

    /**
     * 获得用水账单明细列表,
     *
     * @param queryReqVO 查询条件键值对
     * @return 用水账单明细列表
     */
    List<WaterBillItemDO> getList(WaterBillItemQueryReqVO queryReqVO);

    /**
     * 批量导入用水账单明细 excel
     *
     * @param importList      导入用水账单明细列表
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    ImportExcelRespVO importExcel(List<WaterBillItemExcelVO> importList, boolean isUpdateSupport);
}
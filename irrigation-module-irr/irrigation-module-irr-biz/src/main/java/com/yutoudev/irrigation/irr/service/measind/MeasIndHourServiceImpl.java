package com.yutoudev.irrigation.irr.service.measind;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yutoudev.irrigation.framework.common.pojo.PageParam;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelRespVO;
import com.yutoudev.irrigation.framework.excel.core.ImportExcelResultVO;
import com.yutoudev.irrigation.framework.excel.core.util.DictConvertUtils;
import com.yutoudev.irrigation.framework.excel.core.util.ExcelUtils;
import com.yutoudev.irrigation.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yutoudev.irrigation.irr.controller.admin.equipbase.vo.EquipBaseCacheVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.*;
import com.yutoudev.irrigation.irr.controller.app.measind.vo.ShortRainfallRespVO;
import com.yutoudev.irrigation.irr.convert.measindhour.MeasIndHourConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.equipbase.EquipBaseDO;
import com.yutoudev.irrigation.irr.dal.dataobject.equipindicates.EquipIndicatesDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measreport.MeasReportDO;
import com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndHourMapper;
import com.yutoudev.irrigation.irr.service.equipbase.EquipBaseService;
import com.yutoudev.irrigation.irr.service.equipindicates.EquipIndicatesService;
import com.yutoudev.irrigation.irr.service.measreport.MeasReportService;
import io.github.portaldalaran.talons.core.TalonsHelper;
import io.github.portaldalaran.talons.core.TalonsServiceImpl;
import io.github.portaldalaran.talons.exception.TalonsUniqueException;
import io.github.portaldalaran.taming.mybatisplus.QueryCriteriaWrapperBuilder;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.yutoudev.irrigation.framework.common.exception.enums.GlobalErrorCodeConstants.UNIQUE_FIELD;
import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.EquipErrorCodeConstants.*;
import static com.yutoudev.irrigation.irr.mq.MqConfigConstants.MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE;

/**
 * 量测设备指标统计小时Service实现类
 *
 * <AUTHOR>
 * @description 管理后台-量测设备指标统计小时Service实现类
 * @time 2024-07-27 15:15:00
 */
@Service
@Validated
public class MeasIndHourServiceImpl extends TalonsServiceImpl<MeasIndHourMapper, MeasIndHourDO> implements MeasIndHourService<MeasIndHourDO> {

    @Resource
    private MeasIndHourMapper measIndHourMapper;

    @Resource
    private TalonsHelper talonsHelper;
    @Resource
    private EquipBaseService<EquipBaseDO> equipBaseService;
    @Resource
    private MeasReportService<MeasReportDO> measReportService;
    @Resource
    private EquipIndicatesService<EquipIndicatesDO> equipIndicatesService;

    @Override
    public void checkField(MeasIndHourDO entity) {
        try {
            super.checkField(entity);
        } catch (TalonsUniqueException e) {
            throw exception(UNIQUE_FIELD, e.getMessage(), e.getValue());
        }
    }

    @Override
    public Long create(MeasIndHourCreateReqVO createReqVO) {
        // 插入
        MeasIndHourDO measIndHour = MeasIndHourConvert.INSTANCE.convert(createReqVO);
        this.save(measIndHour, true);
        // 返回
        return measIndHour.getId();
    }

    @Override
    public boolean createBatch(List<MeasIndHourCreateReqVO> list) {

        List<MeasIndHourDO> saveList = MeasIndHourConvert.INSTANCE.convertCreateBatch(list);
        if (this.saveBatch(saveList, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_HOUR_SAVE_BATCH_ERROR);
        }
    }

    @Override
    public void update(MeasIndHourUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateMeasIndHourExists(updateReqVO.getId());
        // 更新
        MeasIndHourDO updateObj = MeasIndHourConvert.INSTANCE.convert(updateReqVO);
        this.updateById(updateObj, true);
    }

    @Override
    public void updateByEquip(EquipBaseCacheVO equip) {
        this.lambdaUpdate()
                .eq(MeasIndHourDO::getEquipId, equip.getId())
                .set(MeasIndHourDO::getCentralId, equip.getCentralId())
                .set(MeasIndHourDO::getDevId, equip.getDevId())
                .update();
    }


    @Override
    public boolean updateBatch(List<MeasIndHourUpdateReqVO> list) {

        List<MeasIndHourDO> updateList = MeasIndHourConvert.INSTANCE.convertUpdateBatch(list);

        for (MeasIndHourDO tempDO : updateList) {
            // 校验存在,因为存进来转化就是UpdateReqVO
            this.validateMeasIndHourExists(tempDO.getId());
        }

        if (this.updateBatchById(updateList, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_HOUR_UPDATE_BATCH_ERROR);
        }
    }

    @Override
    public boolean saveBatch(List<MeasIndHourDO> list) {
        return measIndHourMapper.insertOrUpdateBatch(list);
    }

    @Override
    public void delete(Long id) {
        // 校验存在
        this.validateMeasIndHourExists(id);
        // 删除
        this.removeById(id, true);
    }

    @Override
    public void deleteByEquipAndDateRange(EquipBaseDO equip, LocalDateTime beginTime, LocalDateTime endTime) {
        this.lambdaUpdate()
                .eq(MeasIndHourDO::getEquipId, equip.getId())
                .gt(MeasIndHourDO::getHourTime, beginTime)
                .le(MeasIndHourDO::getHourTime, endTime)
                .remove();
    }

    @Override
    public boolean deleteBatch(List<Long> ids) {
        if (this.removeByIds(ids, true)) {
            return true;
        } else {
            throw exception(MEAS_IND_HOUR_DELETE_BATCH_ERROR);
        }
    }

    private void validateMeasIndHourExists(Long id) {
        if (measIndHourMapper.selectById(id) == null) {
            throw exception(MEAS_IND_HOUR_NOT_EXISTS);
        }
    }

    @Override
    public MeasIndHourDO get(Long id) {
        return this.getById(id, true);
    }

    @Override
    public List<MeasIndHourDO> getList(List<Long> ids) {
        return this.selectBatchIds(ids, true);
    }

    @Override
    public List<MeasIndHourDO> getListByEquipIdsAndTime(List<Long> equipIds, LocalDateTime time) {
        return this.list(new LambdaQueryWrapperX<MeasIndHourDO>()
                .in(MeasIndHourDO::getDevId, equipIds)
                .gt(MeasIndHourDO::getHourTime, time.withHour(8).withMinute(0).withSecond(0))
                .le(MeasIndHourDO::getHourTime, time.plusDays(1).withHour(8).withMinute(0).withSecond(0))
        );
    }

    @Override
    public List<MeasIndHourDO> getListByEquipAndDateRange(EquipBaseDO equip, LocalDateTime beginTime, LocalDateTime endTime) {
        return this.list(new LambdaQueryWrapperX<MeasIndHourDO>()
                .eq(MeasIndHourDO::getEquipId, equip.getId())
                .gt(MeasIndHourDO::getHourTime, beginTime)
                .le(MeasIndHourDO::getHourTime, endTime)
                .orderByAsc(MeasIndHourDO::getHourTime)
        );
    }

    @Override
    public List<MeasIndHourDO> getListByEquipIdsAndTimeRange(List<Long> equipIdList, LocalDateTime startTime, LocalDateTime endTime) {
        return this.list(new LambdaQueryWrapperX<MeasIndHourDO>()
                .in(MeasIndHourDO::getEquipId, equipIdList)
                .gt(MeasIndHourDO::getHourTime, startTime)
                .le(MeasIndHourDO::getHourTime, endTime)
                .orderByAsc(MeasIndHourDO::getHourTime)
                .orderByAsc(MeasIndHourDO::getEquipId)
        );
    }

    @Override
    public MeasIndHourStatHourVO getDataByEquipIdAndStatHours(String centralId, String devId, LocalDateTime morning, LocalDateTime night) {
        return baseMapper.getWaterLevelByStatHour(centralId, devId, morning, night);
    }

    @Override
    public MeasIndHourDO getLastDataByEquipId(String centralId, String devId) {
        return this.getOne(new LambdaQueryWrapperX<MeasIndHourDO>()
                .eq(MeasIndHourDO::getCentralId, centralId)
                .eq(MeasIndHourDO::getDevId, devId)
                .orderByDesc(MeasIndHourDO::getHourTime)
                .last("limit 1")
        );
    }

    @Override
    public MeasIndHourDO getCurrentDataByEquipAndTime(String centralId, String devId, LocalDateTime time) {
        return this.getOne(new LambdaQueryWrapperX<MeasIndHourDO>()
                .eq(MeasIndHourDO::getCentralId, centralId)
                .eq(MeasIndHourDO::getDevId, devId)
                .eq(MeasIndHourDO::getHourTime, time)
        );
    }

    @Override
    public PageResult<MeasIndHourDO> page(MeasIndHourPageReqVO pageReqVO) {
        QueryCriteriaWrapperBuilder<MeasIndHourDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasIndHourDO>() {
        };
        queryBuilder.build(pageReqVO);

        PageParam pageParam = new PageParam(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<MeasIndHourDO> pageResult = measIndHourMapper.selectPage(pageParam, queryBuilder.getQueryWrapper());
        talonsHelper.query(pageResult.getList(), this.getEntityClass(), queryBuilder.getAssociationQueryFields());

        return pageResult;
    }

    @Override
    public List<MeasIndHourDO> getList(MeasIndHourQueryReqVO queryReqVO) {
        QueryCriteriaWrapperBuilder<MeasIndHourDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasIndHourDO>() {
        };
        queryBuilder.build(queryReqVO);

        List<MeasIndHourDO> result = measIndHourMapper.selectList(queryBuilder.getQueryWrapper());
        talonsHelper.query(result, this.getEntityClass(), queryBuilder.getAssociationQueryFields());
        return result;
    }

    @Override
    public ImportExcelRespVO importExcel(List<MeasIndHourExcelVO> importList, boolean isUpdateSupport) {
        if (importList == null || importList.isEmpty()) {
            throw exception(MEAS_IND_HOUR_IMPORT_LIST_IS_EMPTY);
        }

        //todo 如果有数据权限
        ImportExcelRespVO importExcelRespVO = ImportExcelRespVO.builder()
                .insertSuccess(new ArrayList<>())
                .updateSuccess(new ArrayList<>())
                .failures(new ArrayList<>()).build();

        List<MeasIndHourDO> saveList = MeasIndHourConvert.INSTANCE.convertImportExcel(importList);

        for (int i = 0; i < saveList.size(); i++) {
            MeasIndHourDO po = saveList.get(i);
            boolean isSave = Objects.isNull(po.getId());
            boolean isSuccess = false;
            ImportExcelResultVO fail = new ImportExcelResultVO();

            try {
                //todo 如果有关联对象
                DictConvertUtils.fill(po, MeasIndHourExcelVO.class);
                this.checkField(po);
                isSuccess = saveOrUpdate(po);
            } catch (Exception e) {
                fail.setIndex(i + 1);
                fail.setValue(e.getMessage());
            }

            if (isSuccess) {
                ExcelUtils.successResult(importExcelRespVO, new ImportExcelResultVO(i + 1, ""), isSave);
            } else {
                if (fail.getIndex() == 0) {
                    fail.setIndex(i + 1);
                    fail.setValue("未知");
                }
                importExcelRespVO.getFailures().add(fail);
            }
        }
        return importExcelRespVO;
    }

    @Override
    public MeasIndHourDO getByEquipAndHour(String centralId, String devId, LocalDateTime hourTime) {
        return this.getOne(new LambdaQueryWrapperX<MeasIndHourDO>()
                .eq(MeasIndHourDO::getCentralId, centralId)
                .eq(MeasIndHourDO::getDevId, devId)
                .eq(MeasIndHourDO::getHourTime, hourTime));
    }

    @Override
    public MeasIndHourStatisticsRecentlyRespVO statisticsRecently(MeasIndHourQueryReqVO queryReqVO) {
        MeasIndHourStatisticsRecentlyRespVO statisticsRecentlyRespVO = new MeasIndHourStatisticsRecentlyRespVO();
        //查询设备
        EquipBaseDO equipBaseDO = equipBaseService.getByCentralIdAndDevId(queryReqVO.getCentralId(), queryReqVO.getDevId());

        LocalDateTime thisTime = LocalDateTime.now();
        LocalDateTime thisTime3 = thisTime.minusHours(3);
        LocalDateTime thisTime6 = thisTime.minusHours(6);
        LocalDateTime thisTime12 = thisTime.minusHours(12);
        LocalDateTime thisTime24 = thisTime.minusHours(24);
        //最近24小时雨量
        if (Objects.equals(equipBaseDO.getRainfallAlgorithm(), MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE)) {
            //五分钟上报
            EquipIndicatesDO equipIndicatesDO = equipIndicatesService.getOne(new LambdaQueryWrapper<EquipIndicatesDO>()
                    .eq(EquipIndicatesDO::getTypeId, MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE));
            List<MeasReportDO> measReportDOList = measReportService.list(new LambdaQueryWrapper<MeasReportDO>()
                    .eq(MeasReportDO::getCentralId, queryReqVO.getCentralId())
                    .eq(MeasReportDO::getDevId, queryReqVO.getDevId())
                    .eq(MeasReportDO::getTypeCode, equipIndicatesDO.getTypeCode())
                    .gt(MeasReportDO::getReportTime, thisTime24)
                    .lt(MeasReportDO::getReportTime, thisTime)
            );
            if (CollUtil.isNotEmpty(measReportDOList)) {
                statisticsRecentlyRespVO.setH3Value(measReportDOList.stream().filter(item -> item.getReportTime().isAfter(thisTime3)).map(MeasReportDO::getTypeValue).map(Float::parseFloat).reduce(0f, Float::sum));
                statisticsRecentlyRespVO.setH6Value(measReportDOList.stream().filter(item -> item.getReportTime().isAfter(thisTime6)).map(MeasReportDO::getTypeValue).map(Float::parseFloat).reduce(0f, Float::sum));
                statisticsRecentlyRespVO.setH12Value(measReportDOList.stream().filter(item -> item.getReportTime().isAfter(thisTime12)).map(MeasReportDO::getTypeValue).map(Float::parseFloat).reduce(0f, Float::sum));
                statisticsRecentlyRespVO.setH24Value(measReportDOList.stream().filter(item -> item.getReportTime().isAfter(thisTime24)).map(MeasReportDO::getTypeValue).map(Float::parseFloat).reduce(0f, Float::sum));
            }
        } else {
            //小时上报
            List<MeasIndHourDO> measIndHourDOList = this.list(new LambdaQueryWrapper<MeasIndHourDO>()
                    .eq(MeasIndHourDO::getCentralId, queryReqVO.getCentralId())
                    .eq(MeasIndHourDO::getDevId, queryReqVO.getDevId())
                    .gt(MeasIndHourDO::getHourTime, thisTime24)
                    .lt(MeasIndHourDO::getHourTime, thisTime)
            );
            if (CollUtil.isNotEmpty(measIndHourDOList)) {
                statisticsRecentlyRespVO.setH3Value(measIndHourDOList.stream().filter(item -> item.getHourTime().isAfter(thisTime3)).map(MeasIndHourDO::getRainfallVolume).reduce(0f, Float::sum));
                statisticsRecentlyRespVO.setH6Value(measIndHourDOList.stream().filter(item -> item.getHourTime().isAfter(thisTime6)).map(MeasIndHourDO::getRainfallVolume).reduce(0f, Float::sum));
                statisticsRecentlyRespVO.setH12Value(measIndHourDOList.stream().filter(item -> item.getHourTime().isAfter(thisTime12)).map(MeasIndHourDO::getRainfallVolume).reduce(0f, Float::sum));
                statisticsRecentlyRespVO.setH24Value(measIndHourDOList.stream().filter(item -> item.getHourTime().isAfter(thisTime24)).map(MeasIndHourDO::getRainfallVolume).reduce(0f, Float::sum));
            }
        }
        return statisticsRecentlyRespVO;
    }

    @Override
    public List<MeasIndHourDO> waterDatalist(MeasIndHourQueryReqVO queryReqVO) {
        List<MeasIndHourDO> measIndHourDOList = new ArrayList<>();

        //查询五分钟上报
        MeasIndHourQueryReqVO queryReqVONew = new MeasIndHourQueryReqVO();
        BeanUtil.copyProperties(queryReqVO, queryReqVONew);
        QueryCriteriaWrapperBuilder<MeasIndHourDO> queryBuilder = new QueryCriteriaWrapperBuilder<MeasIndHourDO>() {
        };
        queryBuilder.build(queryReqVONew);
        EquipIndicatesDO equipIndicatesDO = equipIndicatesService.getOne(new LambdaQueryWrapper<EquipIndicatesDO>()
                .eq(EquipIndicatesDO::getTypeId, MEAS_INDICATES_TYPE_RAIN_FIVE_MINUTE));
        List<MeasReportDO> measReportDOList = measReportService.list(new LambdaQueryWrapper<MeasReportDO>()
                .eq(MeasReportDO::getCentralId, queryReqVONew.getCentralId())
                .eq(MeasReportDO::getDevId, queryReqVONew.getDevId())
                .eq(MeasReportDO::getTypeCode, equipIndicatesDO.getTypeCode())
                .ge(MeasReportDO::getReportTime, queryReqVONew.getCriteriaParams().stream().filter(item -> item.getName().equals("hourTime")).findFirst().get().getValue())
                .le(MeasReportDO::getReportTime, queryReqVONew.getCriteriaParams().stream().filter(item -> item.getName().equals("hourTime")).findFirst().get().getValue2())
                .orderByDesc(MeasReportDO::getReportTime)
        );
        if (CollUtil.isNotEmpty(measReportDOList)) {
            measIndHourDOList = measReportDOList.stream().map(item -> {
                        MeasIndHourDO measIndHourDO = new MeasIndHourDO();
                        measIndHourDO.setRainfallVolume(Float.parseFloat(item.getTypeValue()));
                        measIndHourDO.setHourTime(item.getReportTime());
                        return measIndHourDO;
                    }
            ).collect(Collectors.toList());
        } else {
            //查询小时上报
            measIndHourDOList = this.getList(queryReqVO);
        }
        Float rainfallVolumeCumulative = 0.0f;
        for (int i = measIndHourDOList.size() - 1; i >= 0; i--) {
            MeasIndHourDO measIndHourDO = measIndHourDOList.get(i);
            rainfallVolumeCumulative += measIndHourDO.getRainfallVolume();
            measIndHourDO.setRainfallVolumeCumulative(rainfallVolumeCumulative);
        }
        return measIndHourDOList;
    }

    @Override
    public List<ShortRainfallRespVO> getShortRainfallList(String centralId, String devId) {
        return baseMapper.getShortRainfall(centralId, devId);
    }
}

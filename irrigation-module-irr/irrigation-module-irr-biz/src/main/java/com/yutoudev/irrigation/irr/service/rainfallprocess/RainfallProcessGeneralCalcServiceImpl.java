package com.yutoudev.irrigation.irr.service.rainfallprocess;

import com.yutoudev.irrigation.irr.controller.admin.catchment.vo.catchmentarea.CatchmentAreaQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindhour.MeasIndHourRainfallSimpleRespVO;
import com.yutoudev.irrigation.irr.convert.measindhour.MeasIndHourConvert;
import com.yutoudev.irrigation.irr.dal.dataobject.catchment.CatchmentAreaDO;
import com.yutoudev.irrigation.irr.dal.dataobject.catchment.CatchmentAreaEquipDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measanalysisday.MeasAnalysisDayDO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndHourDO;
import com.yutoudev.irrigation.irr.dal.dataobject.rainfallprocess.*;
import com.yutoudev.irrigation.irr.enums.RainfallProcessCalcTypeEnum;
import com.yutoudev.irrigation.irr.service.catchment.CatchmentAreaEquipService;
import com.yutoudev.irrigation.irr.service.catchment.CatchmentAreaService;
import com.yutoudev.irrigation.irr.service.measanalysisday.MeasAnalysisDayService;
import com.yutoudev.irrigation.irr.service.measind.MeasIndHourService;
import com.yutoudev.irrigation.irr.service.rainfallprocess.dto.RainfallProcessMergedDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yutoudev.irrigation.irr.enums.ErrorCodeConstants.RAINFALL_PROCESS_BASIN_CALC_STRADDLE_YEAR_ERROR;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RainfallProcessGeneralCalcServiceImpl implements RainfallProcessGeneralCalcService {

    @Resource
    private CatchmentAreaService<CatchmentAreaDO> catchmentAreaService;

    @Resource
    private CatchmentAreaEquipService<CatchmentAreaEquipDO> catchmentAreaEquipService;

    @Resource
    private MeasIndHourService<MeasIndHourDO> measIndHourService;

    @Resource
    private RainfallProcessCalcHelper rainfallProcessCalcHelper;

    @Resource
    private MeasAnalysisDayService<MeasAnalysisDayDO> measAnalysisDayService;

    @Resource
    private RainfallProcessBasinService<RainfallProcessBasinDO> rainfallProcessBasinService;

    @Resource
    private RainfallProcessBasinItemService<RainfallProcessBasinItemDO> rainfallProcessBasinItemService;

    @Resource
    private RainfallProcessSiteService<RainfallProcessSiteDO> rainfallProcessSiteService;

    @Resource
    private RainfallProcessSiteItemService<RainfallProcessSiteItemDO> rainfallProcessSiteItemService;

    @Override
    public void calculate(LocalDateTime startTime, LocalDateTime endTime) {

        if (startTime.getYear() != endTime.getYear()) {
            throw exception(RAINFALL_PROCESS_BASIN_CALC_STRADDLE_YEAR_ERROR);
        }

        // 获取集水面积列表
        List<CatchmentAreaDO> catchmentAreaList = catchmentAreaService.getList(new CatchmentAreaQueryReqVO());

        if (CollectionUtils.isEmpty(catchmentAreaList)) {
            return;
        }

        for (CatchmentAreaDO catchmentArea : catchmentAreaList) {
            List<CatchmentAreaEquipDO> areaEquipList = catchmentAreaEquipService.getListByAreaId(catchmentArea.getId());
            if (CollectionUtils.isEmpty(areaEquipList)) {
                continue;
            }

            // 从设备列表中获取设备ID列表
            List<Long> equipIdList = areaEquipList.stream().map(CatchmentAreaEquipDO::getEquipId).toList();

            // 从hour表中获取数据
            List<MeasIndHourDO> hourList = measIndHourService.getListByEquipIdsAndTimeRange(equipIdList, startTime, endTime);
            List<MeasIndHourRainfallSimpleRespVO> simpleHourList = MeasIndHourConvert.INSTANCE.convertRainfallSimpleList(hourList);

            // 合并上一步列表中的小时数据，生成连续的小时列表
            List<RainfallProcessMergedDTO> merge = rainfallProcessCalcHelper.merge(simpleHourList, RainfallProcessCalcTypeEnum.BASIN.getType(), areaEquipList);
            merge.forEach(merged -> log.info("合并数据：{}", merged));
        }
    }


}

package com.yutoudev.irrigation.irr.controller.admin.usercrop.vo;

import com.yutoudev.irrigation.irr.controller.admin.cropbase.vo.CropBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.userbase.vo.UserBaseRespVO;
import com.yutoudev.irrigation.irr.controller.admin.zonebase.vo.ZoneBaseRespVO;
import lombok.Data;
import lombok.ToString;

/**
 *
 * 用户片区作物DetailResponseVO
 * @description 管理后台-用户片区作物DetailResponseVO
 * <AUTHOR>
 * @time 2024-05-13 15:19:01
 *
 */
@Data
@ToString(callSuper = true)
public class UserCropDetailRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 用水用户ID
     *
     */
    private Long userId;

    /**
     * 用水用户
     *
     */
    private UserBaseRespVO user;

    /**
     * 灌概片区ID
     *
     */
    private Long zoneId;

    /**
     * 灌概片区
     *
     */
    private ZoneBaseRespVO zone;

    /**
     * 作物ID
     *
     */
    private Long cropId;

    /**
     * 作物
     *
     */
    private CropBaseRespVO crop;

    /**
     * 作物面积
     *
     */
    private Double cropArea;
}

package com.yutoudev.irrigation.irr.controller.admin.allocationplanyear.vo;

import lombok.*;
import java.util.*;
import java.time.*;
import java.math.BigDecimal;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.*;
import com.yutoudev.irrigation.framework.common.util.RegexUtils;

/**
 *
 * 配水调度年计划 Base VO
 * @description 配水调度年计划 Base VO，提供给添加、修改、详细的子 VO 使用
 * <AUTHOR>
 * @time 2024-07-23 14:54:37
 *
 */
@Data
public class AllocationPlanYearBaseVO {

    /**
     * 计划名称
     * 
     */
    private String name;

    /**
     * 用水用户ID
     * 
     */
    private Long userId;


    /**
     * 灌溉片区ID
     * 
     */
    private Long zoneId;


    /**
     * 水渠ID
     * 
     */
    private Long chanId;


    /**
     * 水源ID
     * 
     */
    private Long swhsId;


    /**
     * 计划供水
     * @mock （m³）
     */
    private BigDecimal supplyWater;

    /**
     * 计划时间
     * @mock （yyyy）
     */
    private String planTime;

    /**
     * 备注
     * 
     */
    private String note;
}

package com.yutoudev.irrigation.irr.calc;

import com.yutoudev.irrigation.irr.enums.EquipSectionTypeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 计算流量工具类
 *
 * <AUTHOR>
 */

public class CalcFlowHelper {

    private Section section;

    public void init(Section section) {
        this.section = section;
    }

    /**
     * 返回断面面积
     *
     * @return 断面面积
     * @apiNote 若断面设置为空，则返回0
     */
    public Double getSectionArea() {
        if (!checkParameter()) {
            return 0.0d;
        }
        //如果是U型
        if (Objects.equals(EquipSectionTypeEnum.U_SHAPED.getType(), section.getType())) {
            /*
             * 矩形面积+半圆面积
             * 矩形面积 = 2 * 圆半径 * (高度 - 圆半径)
             * 半圆面积 = π * 半径² / 2
             */
            double height = section.getHeight();
            double radius = section.getRadius();
            double area;

            if (height <= radius) {
                // 水深小于等于半径时，过水断面为弓形
                // 弓形对应的圆心角（弧度）
                double alpha = 2 * Math.acos(1 - height / radius);
                // 弓形面积
                area = 0.5 * radius * radius * (alpha - Math.sin(alpha));
            } else {
                // 水深大于半径时，过水断面为矩形加半圆
                double rectangleArea = 2 * radius * (height - radius);
                double semiCircleArea = Math.PI * Math.pow(radius, 2) / 2;
                area = rectangleArea + semiCircleArea;
            }
            return BigDecimal.valueOf(area).setScale(3, RoundingMode.HALF_UP).doubleValue();
        } else {
            //如果是四边形：矩形和梯形,（左边坡系数*左岸水深+右边坡系数*右岸水深 + 底宽*2）/2 *高度
            return BigDecimal.valueOf((section.getLeftSlope() * section.getHeight()
                    + section.getRightSlope() * section.getHeight()
                    + section.getBottomWidth() * 2)
                    / 2 * section.getHeight()).setScale(3, RoundingMode.HALF_UP).doubleValue();
        }
    }

    /**
     * 校验 section 参数是否符合计算要求
     *
     * @return 若参数符合要求返回 true，否则返回 false
     */
    public boolean checkParameter() {
        if (Objects.isNull(section)) {
            return false;
        }

        // 校验公共参数
        if (Objects.isNull(section.getHeight()) || section.getHeight() < 0) {
            return false;
        }


        Integer type = section.getType();
        if (Objects.isNull(type)) {
            return false;
        }

        // 根据不同断面类型校验参数
        if (Objects.equals(EquipSectionTypeEnum.U_SHAPED.getType(), type)) {
            if (Objects.isNull(section.getRadius()) || section.getRadius() <= 0) {
                return false;
            }
        } else {
            if (Objects.isNull(section.getLeftSlope()) || section.getLeftSlope() < 0) {
                return false;
            }
            if (Objects.isNull(section.getRightSlope()) || section.getRightSlope() < 0) {
                return false;
            }
            if (Objects.isNull(section.getBottomWidth()) || section.getBottomWidth() < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 返回湿周
     *
     * @return
     */
    public Double getWettedPerimeter() {
        //底宽+左岸水深*（1+左岸边坡系数^2）^0.5+右岸水深*（1+右岸边坡系数^2）^0.5
        return BigDecimal.valueOf(section.getBottomWidth()
                        + section.getHeight() * Math.pow(1 + Math.pow(section.getLeftSlope(), 2), 0.5)
                        + section.getHeight() * Math.pow(1 + Math.pow(section.getRightSlope(), 2), 0.5))
                .setScale(3, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 水力半径
     *
     * @return
     */
    public Double getHydraulicRadius() {
        //过水面积/湿周
        return BigDecimal.valueOf(getSectionArea() / getWettedPerimeter()).setScale(3, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 流速
     *
     * @return
     */
    public Double getVelocityOfFlow() {
        //（1/糙率）*（水力半径^(2/3)）* (水力坡度^(1/2))
        return BigDecimal.valueOf(1 / section.getRoughness()
                * Math.pow(getHydraulicRadius(), (double) 2 / 3)
                * Math.pow(section.getWaterGrade(), 0.5)).setScale(3, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 获取流量
     *
     * @param waterSpeed 流速
     * @return 流量
     * @apiNote 1、若流速不传，可根据工式计算流速，这时必须有设置糙率、水力坡度。
     * 2、若不传流速，未设备糙率、水力坡度，则返回流量不可信
     * 3、若断面为空则返回0
     */
    public Double getFlow(Double waterSpeed) {
        if (Objects.isNull(section)) {
            return 0.0d;
        }
        //流速*过水面积
        if (Objects.nonNull(waterSpeed) && waterSpeed >= 0) {
            return BigDecimal.valueOf(waterSpeed * getSectionArea()).setScale(3, RoundingMode.HALF_UP).doubleValue();
        }
        return BigDecimal.valueOf(getVelocityOfFlow() * getSectionArea()).setScale(3, RoundingMode.HALF_UP).doubleValue();
    }

}

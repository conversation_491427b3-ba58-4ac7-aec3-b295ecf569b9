package com.yutoudev.irrigation.irr.controller.admin.waterflowconfig.vo.config;

import lombok.*;

import java.time.*;

/**
 *
 * 水流配置ResponseVO
 * @description 管理后台-水流配置ResponseVO
 * <AUTHOR>
 * @time 2024-08-13 15:15:33
 *
 */
@Data
@ToString(callSuper = true)
public class WaterFlowConfigRespVO {


    /**
     * ID
     * 
     */
    private Long id;

    /**
     * 名称
     * 
     */
    private String name;

    /**
     * 备注
     * 
     */
    private String note;

    /**
     * 创建人
     * 
     */
    private String creator;

    /**
     * 创建时间
     * 
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     * 
     */
    private String updater;

    /**
     * 修改时间
     * 
     */
    private LocalDateTime updateTime;
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yutoudev.irrigation.irr.dal.mysql.allocationplan.AllocationPlanMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="queryWaterAssessmentChan" resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.WaterAssessmentChanRespVO">
        SELECT
            t.chan_name chan_name,
            ROUND( EXP( SUM( LOG( t.flow_rate ))) * 100, 2) rate,
            SUM( t.apply_water ) apply_water,
            SUM( t.plan_water ) plan_water,
            SUM( t.water ) water,
            SUM( t.monitor_water ) monitor_water
        FROM
            (
                SELECT
                    aws.chan_id chan_id,
                    aws.chan_name chan_name,
                    ROUND( aws.water / aws.monitor_water, 4) flow_rate,
                    IFNULL( ua.water, 0 ) apply_water,
                    ap.supply_water plan_water,
                    aw.water water,
                    aws.monitor_water monitor_water
                FROM
                    irr_allocation_plan ap
                    LEFT JOIN irr_usewater_apply ua ON ap.apply_id = ua.id
                    LEFT JOIN irr_allocation_water aw ON ap.id = aw.plan_id
                    LEFT JOIN irr_allocation_water_scheme aws ON aw.id = aws.allocation_water_id
                WHERE
                    ap.`status` = 4
                    AND aws.monitor_water > 0
                    AND DATE_FORMAT( aw.start_time,  #{timeFormat} ) = #{time}
            ) t
        GROUP BY
            t.chan_id
    </select>

    <select id="queryWaterAssessmentUser" resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.WaterAssessmentUserRespVO">
        SELECT
            t.`user_id` user_id,
            t.`user_name` user_name,
            SUM( t.apply_water ) apply_water,
            SUM( t.plan_water ) plan_water,
            SUM( t.water ) water,
            SUM( t.monitor_water ) monitor_water,
            SUM( t.quota ) quota,
            SUM( t.imonitor_water ) imonitor_water,
            ROUND( SUM( t.quota )  / SUM( t.imonitor_water ) * 100, 2) quota_rate
        FROM
            (
                SELECT
                    aw.user_id user_id,
                    aw.user_name user_name,
                    IFNULL( ua.water, 0 ) apply_water,
                    ap.supply_water plan_water,
                    aw.water water,
                    aw.monitor_water monitor_water,
                    SUM( awi.quota ) quota,
                    SUM( awi.monitor_water ) imonitor_water
                FROM
                    irr_allocation_plan ap
                        LEFT JOIN irr_usewater_apply ua ON ap.apply_id = ua.id
                        LEFT JOIN irr_allocation_water aw ON ap.id = aw.plan_id
                        LEFT JOIN irr_allocation_water_item awi ON awi.water_id = aw.id
                WHERE
                    ap.`status` = 4
                  AND DATE_FORMAT( aw.start_time, #{timeFormat} ) = #{time}
                  AND aw.use_type = 6
                GROUP BY
                    aw.id
            ) t
        GROUP BY
            t.user_id
    </select>

    <select id="queryWaterAssessmentUserCrop" resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.WaterAssessmentUserCropRespVO">
        SELECT
            awi.crop_name,
            ap.supply_water plan_water,
            aw.water water,
            SUM( awi.crop_area ) crop_area,
            SUM( awi.quota ) quota,
            SUM( awi.monitor_water ) monitor_water,
            ROUND( SUM( awi.quota ) / SUM( awi.monitor_water ) * 100, 2) quota_rate
        FROM
            irr_allocation_plan ap
                LEFT JOIN irr_allocation_water aw ON ap.id = aw.plan_id
                LEFT JOIN irr_allocation_water_item awi ON awi.water_id = aw.id
        WHERE
            ap.`status` = 4
          AND ap.user_id = #{userId}
          AND DATE_FORMAT( aw.start_time, #{timeFormat} ) = #{time}
          AND aw.use_type = 6
        GROUP BY
            awi.crop_id
    </select>

    <select id="queryWaterAssessment" resultType="com.yutoudev.irrigation.irr.controller.admin.statistics.vo.WaterAssessmentRespVO">
        SELECT
            ROUND( EXP( SUM( LOG( tt.flow_rate ))) * 100, 2) chan_rate,
            SUM( tt.apply_water ) apply_water,
            SUM( tt.plan_water ) plan_water,
            SUM( tt.water ) water,
            SUM( tt.monitor_water ) monitor_water
        FROM
            (
                SELECT
                    t.chan_name chan_name,
                    ROUND( EXP( SUM( LOG( t.flow_rate ))), 4) flow_rate,
                    SUM( t.apply_water ) apply_water,
                    SUM( t.plan_water ) plan_water,
                    SUM( t.water ) water,
                    SUM( t.monitor_water ) monitor_water
                FROM
                    (
                        SELECT
                            aws.chan_id chan_id,
                            aws.chan_name chan_name,
                            ROUND( aws.water / aws.monitor_water, 4) flow_rate,
                            IFNULL( ua.water, 0 ) apply_water,
                            ap.supply_water plan_water,
                            aw.water water,
                            aws.monitor_water monitor_water
                        FROM
                            irr_allocation_plan ap
                                LEFT JOIN irr_usewater_apply ua ON ap.apply_id = ua.id
                                LEFT JOIN irr_allocation_water aw ON ap.id = aw.plan_id
                                LEFT JOIN irr_allocation_water_scheme aws ON aw.id = aws.allocation_water_id
                        WHERE
                            ap.`status` = 4
                          AND DATE_FORMAT( aw.start_time, #{timeFormat} ) = #{time}
                    ) t
                GROUP BY
                    t.chan_id
            ) tt
    </select>

    <select id="queryWaterAssessmentUserRate" resultType="java.lang.Double">
        SELECT
            ROUND( SUM( awi.quota ) / SUM( awi.monitor_water ) * 100, 2)  user_rate
        FROM
            irr_allocation_plan ap
                LEFT JOIN irr_allocation_water aw ON ap.id = aw.plan_id
                LEFT JOIN irr_allocation_water_item awi ON awi.water_id = aw.id
        WHERE
            ap.`status` = 4
          AND aw.use_type = 6
          AND DATE_FORMAT( aw.start_time, #{timeFormat} ) = #{time}
    </select>

    <select id="queryInversionListByGreaterThanTime" resultType="com.yutoudev.irrigation.irr.dal.dataobject.allocationplan.AllocationPlanDO">
        SELECT * FROM irr_allocation_plan WHERE inversion_status>0 AND (start_time > NOW() OR end_time > NOW())
        <if test="ids != null">
            AND id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="queryInfoByIdsAndStatus" resultType="java.lang.String">
        SELECT CONCAT('用水单元（',user_name, '）的用水申请： ', supply_water, 'm³') AS result FROM irr_allocation_plan WHERE status != #{status}
        <if test="ids != null">
            AND id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>

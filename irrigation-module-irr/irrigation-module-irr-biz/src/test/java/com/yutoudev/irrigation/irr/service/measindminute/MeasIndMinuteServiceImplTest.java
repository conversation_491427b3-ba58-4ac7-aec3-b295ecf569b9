package com.yutoudev.irrigation.irr.service.measindminute;

import com.google.common.collect.Lists;
import com.yutoudev.irrigation.framework.common.pojo.PageResult;
import com.yutoudev.irrigation.framework.test.core.ut.BaseDbUnitTest;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindminute.MeasIndMinuteCreateReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindminute.MeasIndMinutePageReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindminute.MeasIndMinuteQueryReqVO;
import com.yutoudev.irrigation.irr.controller.admin.measind.vo.measindminute.MeasIndMinuteUpdateReqVO;
import com.yutoudev.irrigation.irr.dal.dataobject.measind.MeasIndMinuteDO;
import com.yutoudev.irrigation.irr.dal.mysql.measind.MeasIndMinuteMapper;
import com.yutoudev.irrigation.irr.service.measind.MeasIndMinuteServiceImpl;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import jakarta.annotation.Resource;
import java.util.List;

import static com.yutoudev.irrigation.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yutoudev.irrigation.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yutoudev.irrigation.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yutoudev.irrigation.framework.test.core.util.RandomUtils.*;
import static com.yutoudev.irrigation.irr.enums.EquipErrorCodeConstants.MEAS_IND_MINUTE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link MeasIndMinuteServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(MeasIndMinuteServiceImpl.class)
public class MeasIndMinuteServiceImplTest extends BaseDbUnitTest {

    @Resource
    private MeasIndMinuteServiceImpl measIndMinuteService;

    @Resource
    private MeasIndMinuteMapper measIndMinuteMapper;

    @Test
    public void testCreateSuccess() {
        // 准备参数
        MeasIndMinuteCreateReqVO reqVO = randomPojo(MeasIndMinuteCreateReqVO.class);

        // 调用
        Long id = measIndMinuteService.create(reqVO);
        // 断言
        assertNotNull(id);
        // 校验记录的属性是否正确
        MeasIndMinuteDO measIndMinute = measIndMinuteMapper.selectById(id);
        assertPojoEquals(reqVO, measIndMinute);
    }

    @Test
    public void testCreateBatchSuccess() {
        // 准备参数
        List<MeasIndMinuteCreateReqVO> list = randomPojoList(MeasIndMinuteCreateReqVO.class);
        // 调用
        boolean isCreateSuccess = measIndMinuteService.createBatch(list);
        // 断言
        assertTrue(isCreateSuccess);
        // 随机根据查询条件去查询一个出来比较
        MeasIndMinuteCreateReqVO reqVO = list.get(RandomUtils.nextInt(0,4));

        // 准备参数
        MeasIndMinutePageReqVO queryVO = new MeasIndMinutePageReqVO();
                queryVO.setEquipId(reqVO.getEquipId());
                queryVO.setCentralId(reqVO.getCentralId());
                queryVO.setDevId(reqVO.getDevId());
                queryVO.setRainfall(reqVO.getRainfall());
        // 调用
        PageResult<MeasIndMinuteDO> pageResult = measIndMinuteService.page(queryVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(reqVO, pageResult.getList().get(0));
    }

    @Test
    public void testUpdateSuccess() {
        // mock 数据
        MeasIndMinuteDO dbMeasIndMinute = randomPojo(MeasIndMinuteDO.class);
        measIndMinuteMapper.insert(dbMeasIndMinute);// @Sql: 先插入出一条存在的数据
        // 准备参数
        MeasIndMinuteUpdateReqVO reqVO = randomPojo(MeasIndMinuteUpdateReqVO.class, o -> {
            o.setId(dbMeasIndMinute.getId()); // 设置更新的 ID
        });

        // 调用
        measIndMinuteService.update(reqVO);
        // 校验是否更新正确
        MeasIndMinuteDO measIndMinute = measIndMinuteMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, measIndMinute);
    }

    @Test
    public void testUpdateBatchSuccess() {
        // mock 数据
        MeasIndMinuteDO dbMeasIndMinuteA = randomPojo(MeasIndMinuteDO.class);
        measIndMinuteMapper.insert(dbMeasIndMinuteA);// @Sql: 先插入出一条存在的数据

        MeasIndMinuteDO dbMeasIndMinuteB = randomPojo(MeasIndMinuteDO.class);
        measIndMinuteMapper.insert(dbMeasIndMinuteB);// @Sql: 再插入出一条存在的数据

        // 准备参数
        MeasIndMinuteUpdateReqVO reqVOA = randomPojo(MeasIndMinuteUpdateReqVO.class, o -> {
             o.setId(dbMeasIndMinuteA.getId()); // 设置更新的 ID
        });
        // 准备参数
        MeasIndMinuteUpdateReqVO reqVOB = randomPojo(MeasIndMinuteUpdateReqVO.class, o -> {
            o.setId(dbMeasIndMinuteB.getId()); // 设置更新的 ID
        });

        // 调用
        List<MeasIndMinuteUpdateReqVO> list = Lists.newArrayList(reqVOA,reqVOB);
        boolean updateSuccess = measIndMinuteService.updateBatch(list);
        assertTrue(updateSuccess);
        // 校验是否更新正确
        MeasIndMinuteDO measIndMinuteA = measIndMinuteMapper.selectById(reqVOA.getId()); // 获取最新的
        assertPojoEquals(reqVOA, measIndMinuteA);

        MeasIndMinuteDO measIndMinuteB = measIndMinuteMapper.selectById(reqVOB.getId()); // 获取最新的
        assertPojoEquals(reqVOB, measIndMinuteB);
    }
    @Test
    public void testUpdateNotExists() {
        // 准备参数
        MeasIndMinuteUpdateReqVO reqVO = randomPojo(MeasIndMinuteUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> measIndMinuteService.update(reqVO), MEAS_IND_MINUTE_NOT_EXISTS);
    }

    @Test
    public void testDeleteSuccess() {
        // mock 数据
        MeasIndMinuteDO dbMeasIndMinute = randomPojo(MeasIndMinuteDO.class);
        measIndMinuteMapper.insert(dbMeasIndMinute);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbMeasIndMinute.getId();

        // 调用
        measIndMinuteService.delete(id);
       // 校验数据不存在了
       assertNull(measIndMinuteMapper.selectById(id));
    }

    @Test
    public void testDeleteBatchSuccess() {
        // mock 数据
        MeasIndMinuteDO dbMeasIndMinuteA = randomPojo(MeasIndMinuteDO.class);
        measIndMinuteMapper.insert(dbMeasIndMinuteA);// @Sql: 先插入出一条存在的数据

        MeasIndMinuteDO dbMeasIndMinuteB = randomPojo(MeasIndMinuteDO.class);
        measIndMinuteMapper.insert(dbMeasIndMinuteB);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long aId = dbMeasIndMinuteA.getId();
        Long bId = dbMeasIndMinuteB.getId();
        List<Long> ids = Lists.newArrayList(aId,bId);
        // 调用
        measIndMinuteService.deleteBatch(ids);
        // 校验数据不存在了
        assertNull(measIndMinuteMapper.selectById(aId));
        assertNull(measIndMinuteMapper.selectById(bId));
    }

            @Test
    public void testDeleteNotExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> measIndMinuteService.delete(id), MEAS_IND_MINUTE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPage() {
       // mock 数据
       MeasIndMinuteDO dbMeasIndMinute = randomPojo(MeasIndMinuteDO.class, o -> { // 等会查询到
           o.setEquipId(null);
           o.setCentralId(null);
           o.setDevId(null);
           o.setReportTime(null);
           o.setRainfall(null);
       });
       measIndMinuteMapper.insert(dbMeasIndMinute);
       // 测试 equipId 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setEquipId(null)));
       // 测试 centralId 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setCentralId(null)));
       // 测试 devId 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setDevId(null)));
       // 测试 reportTime 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setReportTime(null)));
       // 测试 rainfall 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setRainfall(null)));
       // 准备参数
       MeasIndMinutePageReqVO reqVO = new MeasIndMinutePageReqVO();
                reqVO.setEquipId(null);
                reqVO.setCentralId(null);
                reqVO.setDevId(null);
                //reqVO.setBeginReportTime(null);
       //reqVO.setEndReportTime(null);
       reqVO.setReportTime(null);
                reqVO.setRainfall(null);

       // 调用
       PageResult<MeasIndMinuteDO> pageResult = measIndMinuteService.page(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbMeasIndMinute, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetList() {
       // mock 数据
       MeasIndMinuteDO dbMeasIndMinute = randomPojo(MeasIndMinuteDO.class, o -> { // 等会查询到
           o.setEquipId(null);
           o.setCentralId(null);
           o.setDevId(null);
           o.setReportTime(null);
           o.setRainfall(null);
       });
       measIndMinuteMapper.insert(dbMeasIndMinute);
       // 测试 equipId 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setEquipId(null)));
       // 测试 centralId 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setCentralId(null)));
       // 测试 devId 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setDevId(null)));
       // 测试 reportTime 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setReportTime(null)));
       // 测试 rainfall 不匹配
       measIndMinuteMapper.insert(cloneIgnoreId(dbMeasIndMinute, o -> o.setRainfall(null)));
       // 准备参数
       MeasIndMinuteQueryReqVO reqVO = new MeasIndMinuteQueryReqVO();
                reqVO.setEquipId(null);
                reqVO.setCentralId(null);
                reqVO.setDevId(null);
                reqVO.setReportTime(null);
                reqVO.setRainfall(null);

       // 调用
       List<MeasIndMinuteDO> list = measIndMinuteService.getList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbMeasIndMinute, list.get(0));
    }

}

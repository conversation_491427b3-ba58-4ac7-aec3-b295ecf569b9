package com.yutoudev.irrigation.irr.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 站点降雨量算法
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum EquipRainfallAlgorithmEnum {

    /**
     * 5分钟降雨量（40代表量雨站点上报指标列值）
     */
    FIVE_MIN(40, "5分钟降雨量"),

    /**
     * 1小时降雨量（45代表量雨站点上报指标列值）
     */
    ONE_HOUR(45, "1小时降雨量");


    /**
     * 站点类型
     */
    private final Integer type;
    /**
     * 站点类型名称
     */
    private final String name;
}
